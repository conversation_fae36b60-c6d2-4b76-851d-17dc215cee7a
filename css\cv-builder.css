/* ===== CV Builder Styles ===== */

.cv-builder-page {
    background: var(--bg-secondary);
    min-height: 100vh;
}

.cv-builder-nav {
    background: var(--bg-card);
    border-bottom: 1px solid var(--bg-secondary);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.cv-builder-nav .nav-logo a {
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.cv-builder-container {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    height: calc(100vh - 70px);
    overflow: hidden;
}

/* ===== Sidebar Styles ===== */
.cv-sidebar {
    background: var(--bg-card);
    border-left: 1px solid var(--bg-secondary);
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.sidebar-header h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.progress-indicator {
    margin-bottom: var(--spacing-xl);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-sm);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.sidebar-sections {
    margin-bottom: var(--spacing-xl);
}

.section-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    margin-bottom: var(--spacing-sm);
}

.section-item:hover {
    background: var(--bg-secondary);
}

.section-item.active {
    background: var(--primary-color);
    color: var(--text-white);
}

.section-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    color: var(--primary-color);
    font-size: 1.125rem;
}

.section-item.active .section-icon {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
}

.section-info {
    flex: 1;
}

.section-info h4 {
    font-size: 1rem;
    margin-bottom: var(--spacing-xs);
}

.section-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.section-item.active .section-info p {
    color: rgba(255, 255, 255, 0.8);
}

.section-status {
    font-size: 1.125rem;
}

.section-status .fa-check-circle {
    color: var(--accent-color);
}

.section-status .fa-circle {
    color: var(--text-light);
}

/* ===== AI Assistant ===== */
.ai-assistant {
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    color: var(--text-white);
}

.ai-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.ai-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.125rem;
}

.ai-info h4 {
    margin-bottom: var(--spacing-xs);
    font-size: 1rem;
}

.ai-status {
    font-size: 0.875rem;
    opacity: 0.8;
    margin: 0;
}

.ai-suggestions {
    margin-bottom: var(--spacing-md);
}

.suggestion-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    line-height: 1.4;
}

.btn-ai {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    border: 1px solid rgba(255, 255, 255, 0.3);
    width: 100%;
}

.btn-ai:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* ===== Main Editor ===== */
.cv-editor {
    padding: var(--spacing-xl);
    overflow-y: auto;
    background: var(--bg-primary);
}

.editor-section {
    max-width: 800px;
    margin: 0 auto;
}

.section-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.section-header h2 {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1.125rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--bg-card);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* ===== Photo Upload ===== */
.photo-upload-section {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.photo-upload-section h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.photo-upload {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.photo-preview {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--text-light);
    border: 3px dashed var(--bg-secondary);
    transition: var(--transition-normal);
    overflow: hidden;
}

.photo-preview:hover {
    border-color: var(--primary-color);
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-controls {
    display: flex;
    gap: var(--spacing-md);
}

/* ===== Section Actions ===== */
.section-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--bg-secondary);
}

/* ===== CV Preview Panel ===== */
.cv-preview-panel {
    background: var(--bg-card);
    border-right: 1px solid var(--bg-secondary);
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-secondary);
}

.preview-header h3 {
    font-size: 1.125rem;
    color: var(--text-primary);
}

.preview-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.zoom-level {
    font-size: 0.875rem;
    color: var(--text-secondary);
    min-width: 40px;
    text-align: center;
}

.preview-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    min-height: 500px;
}

.cv-preview-document {
    background: var(--bg-card);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-lg);
    transform-origin: top center;
    transition: transform 0.3s ease;
}

.cv-page {
    padding: var(--spacing-xl);
    min-height: 600px;
}

/* ===== CV Preview Content ===== */
.cv-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--primary-color);
}

.cv-photo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
    overflow: hidden;
}

.cv-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cv-basic-info {
    flex: 1;
}

.cv-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.cv-title {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.cv-contact {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.contact-item i {
    color: var(--primary-color);
}

.cv-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.cv-section h3 {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--bg-secondary);
}

/* ===== Experience/Education/Language Items ===== */
.experience-item,
.education-item,
.language-item {
    background: var(--bg-card);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: var(--transition-normal);
}

.experience-item:hover,
.education-item:hover,
.language-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--bg-secondary);
}

.item-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.125rem;
}

.btn-remove {
    background: var(--danger-color);
    color: var(--text-white);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-remove:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.btn-add {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    border: 2px dashed var(--primary-color);
    background: transparent;
    color: var(--primary-color);
    padding: var(--spacing-lg);
    font-size: 1rem;
    transition: var(--transition-normal);
}

.btn-add:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-style: solid;
}

.full-width {
    grid-column: 1 / -1;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* ===== Skills Section ===== */
.skills-categories {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.skill-category h4 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    font-size: 1.125rem;
}

.skills-input {
    margin-bottom: var(--spacing-lg);
}

.skills-input input {
    width: 100%;
    margin-bottom: var(--spacing-md);
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    min-height: 60px;
    padding: var(--spacing-md);
    border: 2px dashed var(--bg-secondary);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
}

.skill-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
}

.skill-remove {
    background: none;
    border: none;
    color: var(--text-white);
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.skill-remove:hover {
    background: rgba(255, 255, 255, 0.2);
}

.ai-suggestions-box {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.ai-suggestions-box h4 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
}

.suggestions-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.suggestion-btn {
    background: var(--bg-card);
    border: 1px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    text-align: right;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.suggestion-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.suggested-skills {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.skill-suggestion {
    background: var(--bg-card);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.skill-suggestion:hover {
    background: var(--primary-color);
    color: var(--text-white);
}

/* ===== Character Count ===== */
.character-count {
    text-align: left;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

/* ===== Completion Modal ===== */
.completion-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

/* ===== Enhanced CV Preview ===== */
.cv-content .cv-section {
    margin-bottom: var(--spacing-xl);
}

.cv-section .experience-list,
.cv-section .education-list,
.cv-section .skills-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.cv-experience-item,
.cv-education-item {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--bg-secondary);
    border-right: 3px solid var(--primary-color);
}

.cv-experience-item h4,
.cv-education-item h4 {
    font-size: 1rem;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
    font-weight: 600;
}

.cv-experience-item .company,
.cv-education-item .institution {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.cv-experience-item .duration,
.cv-education-item .duration {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
    font-style: italic;
}

.cv-experience-item .description,
.cv-education-item .description {
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-secondary);
}

.cv-skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
}

.cv-skill-item {
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    text-align: center;
    font-size: 0.875rem;
    font-weight: 500;
}

.cv-skill-item.soft-skill {
    background: var(--secondary-color);
}

/* ===== Template Specific Styles ===== */
.modern-header {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.classic-header {
    border-bottom: 3px solid var(--primary-color);
    padding: var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.classic-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--primary-color);
}

.creative-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    min-height: 100%;
}

.creative-sidebar {
    background: var(--gradient-primary);
    color: var(--text-white);
    padding: var(--spacing-xl);
}

.creative-main {
    padding: var(--spacing-xl);
}

.creative-photo {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 0 auto var(--spacing-lg);
    overflow: hidden;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.creative-sidebar .section-title {
    color: var(--text-white);
    border-bottom-color: rgba(255, 255, 255, 0.3);
}

.summary-text {
    line-height: 1.6;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.languages-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.cv-language-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.creative-sidebar .cv-language-item {
    background: rgba(255, 255, 255, 0.1);
}

.language-name {
    font-weight: 500;
}

.language-level {
    font-size: 0.875rem;
    color: var(--text-secondary);
    background: var(--primary-color);
    color: var(--text-white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.creative-sidebar .language-level {
    background: rgba(255, 255, 255, 0.2);
}

/* ===== Export Loading ===== */
.export-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: var(--text-white);
}

.loading-content {
    text-align: center;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--text-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Export Modal ===== */
.export-modal {
    max-width: 600px;
}

.export-options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.export-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-normal);
    background: var(--bg-card);
}

.export-option:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.export-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    font-size: 1.5rem;
    flex-shrink: 0;
}

.export-info {
    flex: 1;
}

.export-info h4 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.export-info p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
}

.export-arrow {
    color: var(--text-secondary);
    font-size: 1.125rem;
    transition: var(--transition-fast);
}

.export-option:hover .export-arrow {
    color: var(--primary-color);
    transform: translateX(-3px);
}

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .cv-builder-container {
        grid-template-columns: 280px 1fr 300px;
    }
}

@media (max-width: 768px) {
    .cv-builder-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .cv-sidebar {
        order: 2;
        height: auto;
        max-height: 300px;
    }

    .cv-preview-panel {
        display: none;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .skills-categories {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .completion-actions {
        flex-direction: column;
    }

    .cv-skills-grid {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
}
