// ===== Elashrafy CV - Main Application JavaScript =====

class ElashrafyCV {
    constructor() {
        this.currentUser = null;
        this.currentCV = null;
        this.templates = [];
        this.isLoading = false;
        
        this.init();
    }
    
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.loadTemplates();
        this.handleLoading();
        this.initializeAnimations();
    }
    
    // Setup event listeners
    setupEventListeners() {
        // Navigation
        document.addEventListener('DOMContentLoaded', () => {
            this.setupNavigation();
            this.setupScrollEffects();
        });
        
        // Window events
        window.addEventListener('scroll', () => this.handleScroll());
        window.addEventListener('resize', () => this.handleResize());
        
        // Form submissions
        document.addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Button clicks
        document.addEventListener('click', (e) => this.handleButtonClick(e));
    }
    
    // Handle loading screen
    handleLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        const loadingProgress = document.querySelector('.loading-progress');
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
                
                setTimeout(() => {
                    loadingScreen.style.opacity = '0';
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }, 500);
            }
            
            if (loadingProgress) {
                loadingProgress.style.width = `${progress}%`;
            }
        }, 100);
    }
    
    // Setup navigation
    setupNavigation() {
        const navbar = document.getElementById('navbar');
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');
        const navLinks = document.querySelectorAll('.nav-link');
        
        // Mobile menu toggle
        if (navToggle) {
            navToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });
        }
        
        // Smooth scrolling for navigation links
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                    
                    // Update active link
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                    
                    // Close mobile menu
                    navMenu.classList.remove('active');
                    navToggle.classList.remove('active');
                }
            });
        });
    }
    
    // Handle scroll effects
    handleScroll() {
        const navbar = document.getElementById('navbar');
        const scrollTop = window.pageYOffset;
        
        // Navbar background on scroll
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        // Update active navigation link based on scroll position
        this.updateActiveNavLink();
        
        // Parallax effects
        this.handleParallax();
    }
    
    // Update active navigation link
    updateActiveNavLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');
        
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.clientHeight;
            
            if (window.pageYOffset >= sectionTop && 
                window.pageYOffset < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    }
    
    // Handle parallax effects
    handleParallax() {
        const scrolled = window.pageYOffset;
        const heroShapes = document.querySelectorAll('.shape');
        
        heroShapes.forEach((shape, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            shape.style.transform = `translateY(${yPos}px)`;
        });
    }
    
    // Handle window resize
    handleResize() {
        // Update mobile menu state
        const navMenu = document.getElementById('nav-menu');
        const navToggle = document.getElementById('nav-toggle');
        
        if (window.innerWidth > 768) {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        }
    }
    
    // Handle form submissions
    handleFormSubmit(e) {
        const form = e.target;
        
        if (form.classList.contains('cv-form')) {
            e.preventDefault();
            this.saveCVData(form);
        } else if (form.classList.contains('contact-form')) {
            e.preventDefault();
            this.handleContactForm(form);
        }
    }
    
    // Handle button clicks
    handleButtonClick(e) {
        const button = e.target.closest('button');
        if (!button) return;
        
        const action = button.getAttribute('onclick');
        if (action) return; // Let onclick handle it
        
        // Handle data attributes
        const cvAction = button.dataset.cvAction;
        if (cvAction) {
            this.handleCVAction(cvAction, button);
        }
    }
    
    // Initialize animations
    initializeAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.feature-card, .template-card, .pricing-card')
            .forEach(el => observer.observe(el));
    }
    
    // Load CV templates
    async loadTemplates() {
        try {
            // Simulate loading templates (in real app, this would be an API call)
            this.templates = await this.fetchTemplates();
        } catch (error) {
            console.error('Error loading templates:', error);
            this.showNotification('خطأ في تحميل القوالب', 'error');
        }
    }
    
    // Fetch templates (mock function)
    async fetchTemplates() {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve([
                    { id: 1, name: 'كلاسيكي', category: 'traditional', preview: 'template1.jpg' },
                    { id: 2, name: 'عصري', category: 'modern', preview: 'template2.jpg' },
                    { id: 3, name: 'إبداعي', category: 'creative', preview: 'template3.jpg' },
                    // Add more templates...
                ]);
            }, 1000);
        });
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Get notification icon
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global functions for button actions
function startBuilding() {
    window.location.href = 'cv-builder.html';
}

function viewTemplates() {
    window.location.href = 'templates.html';
}

function showLogin() {
    // Show login modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>تسجيل الدخول</h3>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="login-form">
                    <div class="form-group">
                        <label>البريد الإلكتروني</label>
                        <input type="email" required>
                    </div>
                    <div class="form-group">
                        <label>كلمة المرور</label>
                        <input type="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary btn-large">
                        تسجيل الدخول
                    </button>
                </form>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// Initialize the application
const app = new ElashrafyCV();

// Export for use in other modules
window.ElashrafyCV = ElashrafyCV;
