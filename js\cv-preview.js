// ===== CV Preview and Export Functions =====

class CVPreview {
    constructor() {
        this.cvData = null;
        this.currentTemplate = 1;
        this.exportOptions = {
            format: 'pdf',
            quality: 'high',
            includeQR: true,
            includeNFC: false
        };
        
        this.init();
    }
    
    init() {
        this.loadCVData();
        this.setupRealTimePreview();
        this.setupExportOptions();
    }
    
    loadCVData() {
        // Load CV data from localStorage or API
        const savedData = localStorage.getItem('elashrafy_cv_data');
        if (savedData) {
            this.cvData = JSON.parse(savedData);
            this.renderFullPreview();
        }
    }
    
    setupRealTimePreview() {
        // Listen for changes in CV builder
        document.addEventListener('cvDataUpdated', (e) => {
            this.cvData = e.detail;
            this.renderFullPreview();
        });
    }
    
    renderFullPreview() {
        if (!this.cvData) return;
        
        const previewContainer = document.querySelector('.cv-page');
        if (!previewContainer) return;
        
        // Clear existing content
        previewContainer.innerHTML = '';
        
        // Render based on selected template
        switch (this.currentTemplate) {
            case 1:
                this.renderModernTemplate(previewContainer);
                break;
            case 2:
                this.renderClassicTemplate(previewContainer);
                break;
            case 3:
                this.renderCreativeTemplate(previewContainer);
                break;
            default:
                this.renderModernTemplate(previewContainer);
        }
    }
    
    renderModernTemplate(container) {
        const data = this.cvData;
        
        container.innerHTML = `
            <div class="cv-header modern-header">
                <div class="cv-photo">
                    ${data.personal.photo ? 
                        `<img src="${data.personal.photo}" alt="Profile Photo">` : 
                        '<i class="fas fa-user"></i>'
                    }
                </div>
                <div class="cv-basic-info">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                </div>
            </div>
            
            <div class="cv-content modern-content">
                ${this.renderSummarySection(data.summary)}
                ${this.renderExperienceSection(data.experience)}
                ${this.renderEducationSection(data.education)}
                ${this.renderSkillsSection(data.skills)}
                ${this.renderLanguagesSection(data.languages)}
            </div>
        `;
    }
    
    renderClassicTemplate(container) {
        const data = this.cvData;
        
        container.innerHTML = `
            <div class="cv-header classic-header">
                <div class="header-content">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact classic-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                </div>
                ${data.personal.photo ? 
                    `<div class="cv-photo classic-photo">
                        <img src="${data.personal.photo}" alt="Profile Photo">
                    </div>` : ''
                }
            </div>
            
            <div class="cv-content classic-content">
                ${this.renderSummarySection(data.summary)}
                ${this.renderExperienceSection(data.experience)}
                ${this.renderEducationSection(data.education)}
                ${this.renderSkillsSection(data.skills)}
                ${this.renderLanguagesSection(data.languages)}
            </div>
        `;
    }
    
    renderCreativeTemplate(container) {
        const data = this.cvData;
        
        container.innerHTML = `
            <div class="cv-layout creative-layout">
                <div class="cv-sidebar creative-sidebar">
                    <div class="cv-photo creative-photo">
                        ${data.personal.photo ? 
                            `<img src="${data.personal.photo}" alt="Profile Photo">` : 
                            '<i class="fas fa-user"></i>'
                        }
                    </div>
                    <div class="cv-basic-info">
                        <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                        <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    </div>
                    <div class="cv-contact creative-contact">
                        ${this.renderContactInfo(data.personal)}
                    </div>
                    ${this.renderSkillsSection(data.skills, true)}
                    ${this.renderLanguagesSection(data.languages, true)}
                </div>
                <div class="cv-main creative-main">
                    ${this.renderSummarySection(data.summary)}
                    ${this.renderExperienceSection(data.experience)}
                    ${this.renderEducationSection(data.education)}
                </div>
            </div>
        `;
    }
    
    renderContactInfo(personal) {
        let contactHTML = '';
        
        if (personal.email) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${personal.email}</span>
                </div>
            `;
        }
        
        if (personal.phone) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${personal.phone}</span>
                </div>
            `;
        }
        
        if (personal.city) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${personal.city}</span>
                </div>
            `;
        }
        
        if (personal.website) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-globe"></i>
                    <span>${personal.website}</span>
                </div>
            `;
        }
        
        if (personal.linkedin) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fab fa-linkedin"></i>
                    <span>LinkedIn</span>
                </div>
            `;
        }
        
        return contactHTML;
    }
    
    renderSummarySection(summary) {
        if (!summary || summary.trim() === '') return '';
        
        return `
            <div class="cv-section summary-section">
                <h3 class="section-title">الملخص المهني</h3>
                <p class="summary-text">${summary}</p>
            </div>
        `;
    }
    
    renderExperienceSection(experiences) {
        if (!experiences || experiences.length === 0) return '';
        
        const experienceHTML = experiences.map(exp => `
            <div class="cv-experience-item">
                <h4 class="job-title">${exp.jobTitle}</h4>
                <div class="company">${exp.company}</div>
                <div class="duration">${exp.startDate} - ${exp.endDate || 'حتى الآن'}</div>
                ${exp.description ? `<p class="description">${exp.description}</p>` : ''}
            </div>
        `).join('');
        
        return `
            <div class="cv-section experience-section">
                <h3 class="section-title">الخبرات العملية</h3>
                <div class="experience-list">
                    ${experienceHTML}
                </div>
            </div>
        `;
    }
    
    renderEducationSection(education) {
        if (!education || education.length === 0) return '';
        
        const educationHTML = education.map(edu => `
            <div class="cv-education-item">
                <h4 class="degree">${edu.degree}</h4>
                <div class="institution">${edu.institution}</div>
                <div class="duration">${edu.year}</div>
                ${edu.gpa ? `<div class="gpa">المعدل: ${edu.gpa}</div>` : ''}
                ${edu.details ? `<p class="description">${edu.details}</p>` : ''}
            </div>
        `).join('');
        
        return `
            <div class="cv-section education-section">
                <h3 class="section-title">التعليم</h3>
                <div class="education-list">
                    ${educationHTML}
                </div>
            </div>
        `;
    }
    
    renderSkillsSection(skills, isSidebar = false) {
        if (!skills || (skills.technical?.length === 0 && skills.soft?.length === 0)) return '';
        
        let skillsHTML = '';
        
        if (skills.technical && skills.technical.length > 0) {
            skillsHTML += `
                <div class="skills-category">
                    <h4>المهارات التقنية</h4>
                    <div class="cv-skills-grid">
                        ${skills.technical.map(skill => `<div class="cv-skill-item">${skill}</div>`).join('')}
                    </div>
                </div>
            `;
        }
        
        if (skills.soft && skills.soft.length > 0) {
            skillsHTML += `
                <div class="skills-category">
                    <h4>المهارات الشخصية</h4>
                    <div class="cv-skills-grid">
                        ${skills.soft.map(skill => `<div class="cv-skill-item soft-skill">${skill}</div>`).join('')}
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="cv-section skills-section ${isSidebar ? 'sidebar-section' : ''}">
                <h3 class="section-title">المهارات</h3>
                ${skillsHTML}
            </div>
        `;
    }
    
    renderLanguagesSection(languages, isSidebar = false) {
        if (!languages || languages.length === 0) return '';
        
        const languagesHTML = languages.map(lang => `
            <div class="cv-language-item">
                <span class="language-name">${lang.name}</span>
                <span class="language-level">${lang.level}</span>
            </div>
        `).join('');
        
        return `
            <div class="cv-section languages-section ${isSidebar ? 'sidebar-section' : ''}">
                <h3 class="section-title">اللغات</h3>
                <div class="languages-list">
                    ${languagesHTML}
                </div>
            </div>
        `;
    }
    
    setupExportOptions() {
        // Setup export functionality
        this.setupPDFExport();
        this.setupImageExport();
        this.setupQRGeneration();
    }
    
    setupPDFExport() {
        // PDF export using jsPDF
        window.exportToPDF = () => {
            const element = document.querySelector('.cv-page');
            if (!element) return;
            
            // Show loading
            this.showExportLoading('جاري إنشاء ملف PDF...');
            
            // Use html2canvas and jsPDF
            html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                
                let position = 0;
                
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
                
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }
                
                // Add QR code if enabled
                if (this.exportOptions.includeQR) {
                    this.addQRToPDF(pdf);
                }
                
                pdf.save(`${this.cvData.personal.fullName || 'السيرة_الذاتية'}.pdf`);
                this.hideExportLoading();
            }).catch(error => {
                console.error('Error generating PDF:', error);
                this.hideExportLoading();
                this.showExportError('حدث خطأ أثناء إنشاء ملف PDF');
            });
        };
    }
    
    setupImageExport() {
        window.exportToImage = (format = 'png') => {
            const element = document.querySelector('.cv-page');
            if (!element) return;
            
            this.showExportLoading(`جاري إنشاء صورة ${format.toUpperCase()}...`);
            
            html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = `${this.cvData.personal.fullName || 'السيرة_الذاتية'}.${format}`;
                link.href = canvas.toDataURL(`image/${format}`);
                link.click();
                this.hideExportLoading();
            }).catch(error => {
                console.error('Error generating image:', error);
                this.hideExportLoading();
                this.showExportError('حدث خطأ أثناء إنشاء الصورة');
            });
        };
    }
    
    setupQRGeneration() {
        window.generateQRCode = () => {
            const qrData = this.createQRData();
            const qrContainer = document.createElement('div');
            
            QRCode.toCanvas(qrContainer, qrData, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, (error) => {
                if (error) {
                    console.error('Error generating QR code:', error);
                    return;
                }
                
                // Download QR code
                const canvas = qrContainer.querySelector('canvas');
                const link = document.createElement('a');
                link.download = 'qr-code.png';
                link.href = canvas.toDataURL();
                link.click();
            });
        };
    }
    
    createQRData() {
        const data = this.cvData.personal;
        return `BEGIN:VCARD
VERSION:3.0
FN:${data.fullName || ''}
TITLE:${data.jobTitle || ''}
EMAIL:${data.email || ''}
TEL:${data.phone || ''}
URL:${data.website || ''}
END:VCARD`;
    }
    
    addQRToPDF(pdf) {
        // Add QR code to PDF
        const qrData = this.createQRData();
        // Implementation would require QR code generation library
    }
    
    showExportLoading(message) {
        const loading = document.createElement('div');
        loading.id = 'exportLoading';
        loading.className = 'export-loading';
        loading.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
        document.body.appendChild(loading);
    }
    
    hideExportLoading() {
        const loading = document.getElementById('exportLoading');
        if (loading) {
            loading.remove();
        }
    }
    
    showExportError(message) {
        if (window.app) {
            window.app.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

// Initialize CV Preview when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvPreview = new CVPreview();
});
