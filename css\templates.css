/* ===== Templates Page Styles ===== */

.templates-hero {
    padding: 120px 0 var(--spacing-2xl);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    text-align: center;
}

.templates-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.templates-hero p {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 800px;
    margin: 0 auto var(--spacing-2xl);
    line-height: 1.6;
}

/* ===== Search and Filters ===== */
.search-filters {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.search-box {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.search-box i {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xl) var(--spacing-md) var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-lg);
    font-size: 1rem;
    font-family: var(--font-primary);
    transition: var(--transition-fast);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.filter-categories {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
    justify-content: center;
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: 2px solid var(--bg-secondary);
    background: var(--bg-card);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-normal);
    white-space: nowrap;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

.sort-options select {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    background: var(--bg-card);
    color: var(--text-primary);
    font-family: var(--font-primary);
    cursor: pointer;
}

/* ===== Templates Section ===== */
.templates-section {
    padding: var(--spacing-2xl) 0;
    background: var(--bg-primary);
}

.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.template-card {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.template-preview {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.template-image {
    width: 100%;
    height: 100%;
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
}

.template-placeholder {
    width: 200px;
    height: 250px;
    background: var(--bg-card);
    border-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-md);
    position: relative;
    overflow: hidden;
}

/* Template Styles */
.modern-template {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-white);
}

.classic-template {
    background: var(--bg-card);
    border: 2px solid var(--text-primary);
    color: var(--text-primary);
}

.creative-template {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
    color: var(--text-white);
}

.minimal-template {
    background: var(--bg-card);
    border-right: 4px solid var(--primary-color);
    color: var(--text-primary);
}

.professional-template {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: var(--text-white);
}

.template-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.template-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
}

.template-info {
    flex: 1;
}

.template-name {
    height: 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-xs);
}

.template-title {
    height: 8px;
    width: 70%;
    background: rgba(255, 255, 255, 0.6);
    border-radius: var(--radius-sm);
}

.template-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.template-section {
    height: 30px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: var(--radius-sm);
}

.template-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    opacity: 0;
    transition: var(--transition-normal);
}

.template-card:hover .template-overlay {
    opacity: 1;
}

.template-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 2;
}

.template-badge.popular {
    background: var(--secondary-color);
    color: var(--text-white);
}

.template-badge.new {
    background: var(--accent-color);
    color: var(--text-white);
}

.template-badge.premium {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.template-info {
    padding: var(--spacing-lg);
}

.template-info h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.template-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: 0.875rem;
}

.template-tags {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
    margin-bottom: var(--spacing-md);
}

.tag {
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

.template-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.template-stats span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.template-stats i {
    color: var(--primary-color);
}

/* ===== Load More Section ===== */
.load-more-section {
    text-align: center;
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--bg-secondary);
}

/* ===== Template Preview Modal ===== */
.template-preview-content {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
}

.preview-container {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
}

.template-preview-large {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 1.125rem;
}

.preview-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-md);
}

/* ===== Animations ===== */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ===== Template Preview Detailed ===== */
.template-preview-detailed {
    width: 100%;
    min-height: 500px;
    padding: var(--spacing-xl);
    border-radius: var(--radius-md);
    color: var(--text-white);
    position: relative;
    overflow: hidden;
}

.template-preview-detailed.modern-template {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.template-preview-detailed.classic-template {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 2px solid var(--text-primary);
}

.template-preview-detailed.creative-template {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    background-size: 400% 400%;
    animation: gradientShift 3s ease infinite;
}

.template-preview-detailed.minimal-template {
    background: var(--bg-card);
    color: var(--text-primary);
    border-right: 6px solid var(--primary-color);
}

.template-preview-detailed.professional-template {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.preview-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid rgba(255, 255, 255, 0.3);
}

.template-preview-detailed.classic-template .preview-header,
.template-preview-detailed.minimal-template .preview-header {
    border-bottom-color: var(--text-primary);
}

.preview-photo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.8);
}

.template-preview-detailed.classic-template .preview-photo,
.template-preview-detailed.minimal-template .preview-photo {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.preview-info h2 {
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
    font-weight: 700;
}

.preview-info h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
    opacity: 0.9;
}

.preview-contact {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.preview-contact span {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
    opacity: 0.8;
}

.preview-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.preview-section h4 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-md);
    font-weight: 600;
    position: relative;
    padding-bottom: var(--spacing-sm);
}

.preview-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
}

.template-preview-detailed.classic-template .preview-section h4::after,
.template-preview-detailed.minimal-template .preview-section h4::after {
    background: var(--primary-color);
}

.preview-section p {
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: var(--spacing-md);
}

.experience-item {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
}

.template-preview-detailed.classic-template .experience-item,
.template-preview-detailed.minimal-template .experience-item {
    background: var(--bg-secondary);
}

.experience-item h5 {
    font-size: 1rem;
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
}

.experience-item span {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* ===== No Templates Found ===== */
.no-templates {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.no-templates i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.no-templates h3 {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.no-templates p {
    font-size: 1.125rem;
    margin: 0;
}

/* ===== Loading States ===== */
.template-card.loading {
    pointer-events: none;
}

.template-card.loading .template-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 400% 100%;
    animation: shimmer 1.2s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

/* ===== Hover Effects ===== */
.template-card {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
    z-index: 1;
}

.template-card:hover::before {
    left: 100%;
}

.template-stats span:hover {
    color: var(--primary-color);
    transform: scale(1.05);
    transition: all 0.2s ease;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .templates-hero h1 {
        font-size: 2.5rem;
    }

    .templates-hero p {
        font-size: 1.125rem;
    }

    .search-filters {
        gap: var(--spacing-md);
    }

    .filter-categories {
        gap: var(--spacing-xs);
    }

    .filter-btn {
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: 0.875rem;
    }

    .templates-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .template-card {
        max-width: 400px;
        margin: 0 auto;
    }

    .template-preview-content {
        width: 98%;
        max-height: 95vh;
    }

    .preview-actions {
        flex-direction: column;
    }

    .preview-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .preview-contact {
        justify-content: center;
        gap: var(--spacing-md);
    }

    .template-preview-detailed {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .templates-hero {
        padding: 100px 0 var(--spacing-xl);
    }

    .templates-hero h1 {
        font-size: 2rem;
    }

    .search-box {
        max-width: 100%;
    }

    .filter-categories {
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--spacing-sm);
    }

    .template-overlay {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .template-overlay .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }

    .preview-contact {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .template-preview-detailed {
        min-height: 400px;
    }

    .preview-info h2 {
        font-size: 1.5rem;
    }

    .preview-info h3 {
        font-size: 1.125rem;
    }
}
