// ===== CV Builder JavaScript =====

class CVBuilder {
    constructor() {
        this.currentSection = 'personal';
        this.cvData = {
            personal: {},
            summary: '',
            experience: [],
            education: [],
            skills: [],
            languages: [],
            projects: [],
            certifications: []
        };
        this.currentTemplate = 1;
        this.isPreviewMode = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSavedData();
        this.updateProgress();
        this.initializePhotoUpload();
        this.setupAutoSave();
    }
    
    setupEventListeners() {
        // Section navigation
        document.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = item.dataset.section;
                this.switchSection(section);
            });
        });
        
        // Form inputs
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.handleInputChange(e);
            }
        });
        
        // Photo upload
        const photoInput = document.getElementById('photoInput');
        if (photoInput) {
            photoInput.addEventListener('change', (e) => this.handlePhotoUpload(e));
        }
        
        // Real-time preview updates
        this.setupRealTimePreview();
    }
    
    switchSection(sectionName) {
        // Update sidebar
        document.querySelectorAll('.section-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // Update editor content
        document.querySelectorAll('.editor-section').forEach(section => {
            section.style.display = 'none';
        });
        
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.style.display = 'block';
        } else {
            this.createSectionContent(sectionName);
        }
        
        this.currentSection = sectionName;
        this.updateProgress();
    }
    
    createSectionContent(sectionName) {
        const editor = document.querySelector('.cv-editor');
        let sectionHTML = '';
        
        switch (sectionName) {
            case 'summary':
                sectionHTML = this.createSummarySection();
                break;
            case 'experience':
                sectionHTML = this.createExperienceSection();
                break;
            case 'education':
                sectionHTML = this.createEducationSection();
                break;
            case 'skills':
                sectionHTML = this.createSkillsSection();
                break;
            case 'languages':
                sectionHTML = this.createLanguagesSection();
                break;
        }
        
        if (sectionHTML) {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'editor-section';
            sectionDiv.id = `${sectionName}-section`;
            sectionDiv.innerHTML = sectionHTML;
            editor.appendChild(sectionDiv);
        }
    }
    
    createSummarySection() {
        return `
            <div class="section-header">
                <h2>الملخص المهني</h2>
                <p>اكتب نبذة مختصرة عن خبراتك وأهدافك المهنية</p>
            </div>
            
            <div class="form-group">
                <label>الملخص المهني *</label>
                <textarea id="professionalSummary" rows="6" 
                    placeholder="مثال: مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة باستخدام React و Node.js. أسعى لتطوير حلول تقنية مبتكرة تساهم في نمو الشركة وتحسين تجربة المستخدم."
                    data-field="summary"></textarea>
                <div class="character-count">
                    <span id="summaryCount">0</span> / 500 حرف
                </div>
            </div>
            
            <div class="ai-suggestions-box">
                <h4><i class="fas fa-robot"></i> اقتراحات المساعد الذكي</h4>
                <div class="suggestions-list">
                    <button class="suggestion-btn" onclick="applySuggestion('summary1')">
                        أضف المزيد عن خبراتك التقنية
                    </button>
                    <button class="suggestion-btn" onclick="applySuggestion('summary2')">
                        اذكر إنجازاتك الرئيسية
                    </button>
                    <button class="suggestion-btn" onclick="applySuggestion('summary3')">
                        أضف أهدافك المهنية
                    </button>
                </div>
            </div>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: الخبرات العملية
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createExperienceSection() {
        return `
            <div class="section-header">
                <h2>الخبرات العملية</h2>
                <p>أضف تاريخك المهني والوظائف التي شغلتها</p>
            </div>
            
            <div class="experience-list" id="experienceList">
                <!-- Experience items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addExperience()">
                <i class="fas fa-plus"></i>
                إضافة خبرة عملية
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: التعليم
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createEducationSection() {
        return `
            <div class="section-header">
                <h2>التعليم</h2>
                <p>أضف مؤهلاتك الأكاديمية والشهادات</p>
            </div>
            
            <div class="education-list" id="educationList">
                <!-- Education items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addEducation()">
                <i class="fas fa-plus"></i>
                إضافة مؤهل أكاديمي
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: المهارات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createSkillsSection() {
        return `
            <div class="section-header">
                <h2>المهارات</h2>
                <p>أضف مهاراتك التقنية والشخصية</p>
            </div>
            
            <div class="skills-categories">
                <div class="skill-category">
                    <h4>المهارات التقنية</h4>
                    <div class="skills-input">
                        <input type="text" id="technicalSkillInput" placeholder="أدخل مهارة تقنية واضغط Enter">
                        <div class="skills-list" id="technicalSkillsList"></div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h4>المهارات الشخصية</h4>
                    <div class="skills-input">
                        <input type="text" id="softSkillInput" placeholder="أدخل مهارة شخصية واضغط Enter">
                        <div class="skills-list" id="softSkillsList"></div>
                    </div>
                </div>
            </div>
            
            <div class="ai-suggestions-box">
                <h4><i class="fas fa-robot"></i> مهارات مقترحة</h4>
                <div class="suggested-skills">
                    <button class="skill-suggestion" onclick="addSuggestedSkill('JavaScript', 'technical')">JavaScript</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('React', 'technical')">React</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('Node.js', 'technical')">Node.js</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('العمل الجماعي', 'soft')">العمل الجماعي</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('القيادة', 'soft')">القيادة</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('التواصل', 'soft')">التواصل</button>
                </div>
            </div>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: اللغات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createLanguagesSection() {
        return `
            <div class="section-header">
                <h2>اللغات</h2>
                <p>أضف اللغات التي تتقنها ومستوى إتقانك لكل منها</p>
            </div>
            
            <div class="languages-list" id="languagesList">
                <!-- Language items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addLanguage()">
                <i class="fas fa-plus"></i>
                إضافة لغة
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="finishCV()">
                    إنهاء وحفظ
                    <i class="fas fa-check"></i>
                </button>
            </div>
        `;
    }
    
    handleInputChange(e) {
        const field = e.target.dataset.field || e.target.id;
        const value = e.target.value;

        // Update CV data based on current section
        if (this.currentSection === 'personal') {
            this.cvData.personal[field] = value;
        } else if (field === 'professionalSummary' || field === 'summary') {
            this.cvData.summary = value;
        }

        // Update character count for summary
        if (field === 'professionalSummary') {
            const countElement = document.getElementById('summaryCount');
            if (countElement) {
                countElement.textContent = value.length;

                // Change color based on length
                if (value.length < 50) {
                    countElement.style.color = 'var(--danger-color)';
                } else if (value.length > 500) {
                    countElement.style.color = 'var(--warning-color)';
                } else {
                    countElement.style.color = 'var(--success-color)';
                }
            }
        }

        // Update preview in real-time
        this.updatePreview();

        // Mark section as completed
        this.markSectionCompleted(this.currentSection);

        // Auto-save
        this.saveToLocalStorage();

        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('cvDataUpdated', {
            detail: this.cvData
        }));
    }
    
    handlePhotoUpload(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
                const photoPreview = document.getElementById('photoPreview');
                photoPreview.innerHTML = `<img src="${event.target.result}" alt="Profile Photo">`;
                
                // Update CV data
                this.cvData.personal.photo = event.target.result;
                this.updatePreview();
            };
            reader.readAsDataURL(file);
        }
    }
    
    initializePhotoUpload() {
        // Drag and drop functionality
        const photoPreview = document.getElementById('photoPreview');
        if (photoPreview) {
            photoPreview.addEventListener('dragover', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--primary-color)';
            });
            
            photoPreview.addEventListener('dragleave', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--bg-secondary)';
            });
            
            photoPreview.addEventListener('drop', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--bg-secondary)';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const photoInput = document.getElementById('photoInput');
                    photoInput.files = files;
                    this.handlePhotoUpload({ target: photoInput });
                }
            });
        }
    }
    
    updatePreview() {
        // Update CV preview in real-time
        const preview = document.getElementById('cvPreview');
        if (preview) {
            this.renderCVPreview();
        }
    }
    
    renderCVPreview() {
        const data = this.cvData;
        const preview = document.querySelector('.cv-page');

        if (!preview) return;

        // Update basic info
        const nameElement = preview.querySelector('.cv-name');
        const titleElement = preview.querySelector('.cv-title');
        const photoElement = preview.querySelector('.cv-photo');

        if (nameElement) {
            nameElement.textContent = data.personal.fullName || 'اسمك هنا';
            nameElement.style.opacity = data.personal.fullName ? '1' : '0.5';
        }

        if (titleElement) {
            titleElement.textContent = data.personal.jobTitle || 'المسمى الوظيفي';
            titleElement.style.opacity = data.personal.jobTitle ? '1' : '0.5';
        }

        if (photoElement) {
            if (data.personal.photo) {
                photoElement.innerHTML = `<img src="${data.personal.photo}" alt="Profile Photo" style="width: 100%; height: 100%; object-fit: cover;">`;
            } else {
                photoElement.innerHTML = '<i class="fas fa-user"></i>';
            }
        }

        // Update contact info
        this.updateContactInfo();

        // Update summary
        this.updateSummaryPreview();

        // Update other sections
        this.updateExperiencePreview();
        this.updateEducationPreview();
        this.updateSkillsPreview();
        this.updateLanguagesPreview();
    }
    
    updateContactInfo() {
        const contactContainer = document.querySelector('.cv-contact');
        if (!contactContainer) return;
        
        const data = this.cvData.personal;
        let contactHTML = '';
        
        if (data.email) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${data.email}</span>
                </div>
            `;
        }
        
        if (data.phone) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${data.phone}</span>
                </div>
            `;
        }
        
        if (data.city) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${data.city}</span>
                </div>
            `;
        }
        
        contactContainer.innerHTML = contactHTML;
    }
    
    updateSummaryPreview() {
        const summarySection = document.querySelector('.cv-content .cv-section');
        if (summarySection) {
            const summaryText = summarySection.querySelector('p');
            if (summaryText) {
                summaryText.textContent = this.cvData.summary || 'سيتم عرض الملخص المهني هنا...';
                summaryText.style.opacity = this.cvData.summary ? '1' : '0.5';
            }
        }
    }

    updateExperiencePreview() {
        // This will be implemented when experience data is available
        console.log('Experience preview updated');
    }

    updateEducationPreview() {
        // This will be implemented when education data is available
        console.log('Education preview updated');
    }

    updateSkillsPreview() {
        // This will be implemented when skills data is available
        console.log('Skills preview updated');
    }

    updateLanguagesPreview() {
        // This will be implemented when languages data is available
        console.log('Languages preview updated');
    }
    
    markSectionCompleted(sectionName) {
        const sectionItem = document.querySelector(`[data-section="${sectionName}"]`);
        const statusIcon = sectionItem.querySelector('.section-status i');
        
        if (this.isSectionComplete(sectionName)) {
            statusIcon.className = 'fas fa-check-circle';
            statusIcon.style.color = 'var(--accent-color)';
        }
    }
    
    isSectionComplete(sectionName) {
        switch (sectionName) {
            case 'personal':
                return this.cvData.personal.fullName &&
                       this.cvData.personal.jobTitle &&
                       this.cvData.personal.email &&
                       this.cvData.personal.phone;
            case 'summary':
                return this.cvData.summary && this.cvData.summary.length >= 50;
            case 'experience':
                return this.cvData.experience && this.cvData.experience.length > 0;
            case 'education':
                return this.cvData.education && this.cvData.education.length > 0;
            case 'skills':
                return (this.cvData.skills.technical && this.cvData.skills.technical.length > 0) ||
                       (this.cvData.skills.soft && this.cvData.skills.soft.length > 0);
            case 'languages':
                return this.cvData.languages && this.cvData.languages.length > 0;
            default:
                return false;
        }
    }
    
    updateProgress() {
        const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
        const completedSections = sections.filter(section => this.isSectionComplete(section));

        const totalSections = sections.length;
        const progress = (completedSections.length / totalSections) * 100;

        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
            progressFill.style.transition = 'width 0.3s ease';
        }

        if (progressText) {
            progressText.textContent = `${Math.round(progress)}% مكتمل`;

            // Change color based on progress
            if (progress < 30) {
                progressText.style.color = 'var(--danger-color)';
            } else if (progress < 70) {
                progressText.style.color = 'var(--warning-color)';
            } else {
                progressText.style.color = 'var(--success-color)';
            }
        }

        // Update section status indicators
        sections.forEach(section => {
            const sectionItem = document.querySelector(`[data-section="${section}"]`);
            if (sectionItem) {
                const statusIcon = sectionItem.querySelector('.section-status i');
                if (statusIcon) {
                    if (this.isSectionComplete(section)) {
                        statusIcon.className = 'fas fa-check-circle';
                        statusIcon.style.color = 'var(--success-color)';
                    } else {
                        statusIcon.className = 'fas fa-circle';
                        statusIcon.style.color = 'var(--text-light)';
                    }
                }
            }
        });
    }
    
    setupAutoSave() {
        setInterval(() => {
            this.saveToLocalStorage();
        }, 30000); // Auto-save every 30 seconds
    }
    
    saveToLocalStorage() {
        localStorage.setItem('elashrafy_cv_data', JSON.stringify(this.cvData));
        localStorage.setItem('elashrafy_cv_current_section', this.currentSection);
    }
    
    loadSavedData() {
        const savedData = localStorage.getItem('elashrafy_cv_data');
        const savedSection = localStorage.getItem('elashrafy_cv_current_section');
        
        if (savedData) {
            this.cvData = JSON.parse(savedData);
            this.populateForm();
        }
        
        if (savedSection) {
            this.switchSection(savedSection);
        }
    }
    
    populateForm() {
        // Populate personal information
        Object.keys(this.cvData.personal).forEach(key => {
            const input = document.getElementById(key);
            if (input) {
                input.value = this.cvData.personal[key];
            }
        });
        
        // Update preview
        this.updatePreview();
    }
}

// Global functions
function nextSection() {
    const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
    const currentIndex = sections.indexOf(window.cvBuilder.currentSection);
    if (currentIndex < sections.length - 1) {
        window.cvBuilder.switchSection(sections[currentIndex + 1]);
    }
}

function previousSection() {
    const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
    const currentIndex = sections.indexOf(window.cvBuilder.currentSection);
    if (currentIndex > 0) {
        window.cvBuilder.switchSection(sections[currentIndex - 1]);
    }
}

function removePhoto() {
    const photoPreview = document.getElementById('photoPreview');
    photoPreview.innerHTML = '<i class="fas fa-user"></i>';
    
    window.cvBuilder.cvData.personal.photo = null;
    window.cvBuilder.updatePreview();
}

function saveCV() {
    window.cvBuilder.saveToLocalStorage();
    // Show success notification
    if (window.app) {
        window.app.showNotification('تم حفظ السيرة الذاتية بنجاح', 'success');
    }
}

function previewCV() {
    // Open preview in new window or modal
    window.open('cv-preview.html', '_blank');
}

function exportCV() {
    // Show export options modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content export-modal">
            <div class="modal-header">
                <h3><i class="fas fa-download"></i> تصدير السيرة الذاتية</h3>
                <button class="modal-close" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <div class="export-option" onclick="exportToPDF()">
                        <div class="export-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="export-info">
                            <h4>تصدير PDF</h4>
                            <p>ملف PDF عالي الجودة للطباعة والمشاركة</p>
                        </div>
                        <div class="export-arrow">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                    </div>

                    <div class="export-option" onclick="exportToImage('png')">
                        <div class="export-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="export-info">
                            <h4>تصدير صورة PNG</h4>
                            <p>صورة عالية الجودة مناسبة للويب</p>
                        </div>
                        <div class="export-arrow">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                    </div>

                    <div class="export-option" onclick="exportToImage('jpg')">
                        <div class="export-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="export-info">
                            <h4>تصدير صورة JPG</h4>
                            <p>صورة مضغوطة بحجم أصغر</p>
                        </div>
                        <div class="export-arrow">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                    </div>

                    <div class="export-option" onclick="generateQRCode()">
                        <div class="export-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="export-info">
                            <h4>إنشاء رمز QR</h4>
                            <p>رمز QR يحتوي على معلومات الاتصال</p>
                        </div>
                        <div class="export-arrow">
                            <i class="fas fa-chevron-left"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// Additional CV Builder Functions

function addExperience() {
    const experienceList = document.getElementById('experienceList');
    if (!experienceList) return;

    const experienceId = Date.now();

    const experienceItem = document.createElement('div');
    experienceItem.className = 'experience-item';
    experienceItem.dataset.id = experienceId;

    experienceItem.innerHTML = `
        <div class="item-header">
            <h4>خبرة عملية جديدة</h4>
            <button class="btn-remove" onclick="removeExperience(${experienceId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>المسمى الوظيفي *</label>
                <input type="text" name="jobTitle" placeholder="مثل: مطور ويب أول" required
                       onchange="updateExperienceData(${experienceId})">
            </div>
            <div class="form-group">
                <label>اسم الشركة *</label>
                <input type="text" name="company" placeholder="مثل: شركة التقنية المتقدمة" required
                       onchange="updateExperienceData(${experienceId})">
            </div>
            <div class="form-group">
                <label>تاريخ البداية *</label>
                <input type="month" name="startDate" required onchange="updateExperienceData(${experienceId})">
            </div>
            <div class="form-group">
                <label>تاريخ النهاية</label>
                <input type="month" name="endDate" onchange="updateExperienceData(${experienceId})">
                <label class="checkbox-label">
                    <input type="checkbox" name="isCurrent" onchange="toggleCurrentJob(this, ${experienceId})">
                    <span>أعمل حالياً في هذه الوظيفة</span>
                </label>
            </div>
            <div class="form-group full-width">
                <label>وصف المهام والإنجازات</label>
                <textarea rows="4" name="description" placeholder="اكتب وصفاً مفصلاً عن مهامك وإنجازاتك في هذه الوظيفة..."
                          onchange="updateExperienceData(${experienceId})"></textarea>
            </div>
        </div>
    `;

    experienceList.appendChild(experienceItem);

    // Initialize experience data
    if (!window.cvBuilder.cvData.experience) {
        window.cvBuilder.cvData.experience = [];
    }

    window.cvBuilder.cvData.experience.push({
        id: experienceId,
        jobTitle: '',
        company: '',
        startDate: '',
        endDate: '',
        isCurrent: false,
        description: ''
    });

    // Add animation
    experienceItem.style.opacity = '0';
    experienceItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        experienceItem.style.transition = 'all 0.3s ease';
        experienceItem.style.opacity = '1';
        experienceItem.style.transform = 'translateY(0)';
    }, 100);

    // Update progress
    window.cvBuilder.updateProgress();
}

function updateExperienceData(experienceId) {
    const experienceItem = document.querySelector(`[data-id="${experienceId}"]`);
    if (!experienceItem || !window.cvBuilder) return;

    const experience = window.cvBuilder.cvData.experience.find(exp => exp.id === experienceId);
    if (!experience) return;

    // Update experience data from form inputs
    const inputs = experienceItem.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            experience[input.name] = input.checked;
        } else {
            experience[input.name] = input.value;
        }
    });

    // Update preview and progress
    window.cvBuilder.updatePreview();
    window.cvBuilder.updateProgress();
    window.cvBuilder.saveToLocalStorage();
}

function removeExperience(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        // Remove from data
        if (window.cvBuilder && window.cvBuilder.cvData.experience) {
            window.cvBuilder.cvData.experience = window.cvBuilder.cvData.experience.filter(exp => exp.id !== id);
            window.cvBuilder.updateProgress();
            window.cvBuilder.saveToLocalStorage();
        }

        // Remove from DOM with animation
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function addEducation() {
    const educationList = document.getElementById('educationList');
    const educationId = Date.now();

    const educationItem = document.createElement('div');
    educationItem.className = 'education-item';
    educationItem.dataset.id = educationId;

    educationItem.innerHTML = `
        <div class="item-header">
            <h4>مؤهل أكاديمي جديد</h4>
            <button class="btn-remove" onclick="removeEducation(${educationId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>الدرجة العلمية *</label>
                <input type="text" placeholder="مثل: بكالوريوس علوم الحاسب" required>
            </div>
            <div class="form-group">
                <label>اسم الجامعة/المؤسسة *</label>
                <input type="text" placeholder="مثل: جامعة الملك سعود" required>
            </div>
            <div class="form-group">
                <label>سنة التخرج *</label>
                <input type="number" min="1950" max="2030" placeholder="2020" required>
            </div>
            <div class="form-group">
                <label>المعدل التراكمي</label>
                <input type="text" placeholder="3.8 من 4.0">
            </div>
            <div class="form-group full-width">
                <label>تفاصيل إضافية</label>
                <textarea rows="3" placeholder="اذكر أي تفاصيل إضافية مثل التخصص الفرعي، المشاريع المميزة، إلخ..."></textarea>
            </div>
        </div>
    `;

    educationList.appendChild(educationItem);

    // Add animation
    educationItem.style.opacity = '0';
    educationItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        educationItem.style.transition = 'all 0.3s ease';
        educationItem.style.opacity = '1';
        educationItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeEducation(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function addLanguage() {
    const languagesList = document.getElementById('languagesList');
    const languageId = Date.now();

    const languageItem = document.createElement('div');
    languageItem.className = 'language-item';
    languageItem.dataset.id = languageId;

    languageItem.innerHTML = `
        <div class="item-header">
            <h4>لغة جديدة</h4>
            <button class="btn-remove" onclick="removeLanguage(${languageId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>اسم اللغة *</label>
                <input type="text" placeholder="مثل: الإنجليزية" required>
            </div>
            <div class="form-group">
                <label>مستوى الإتقان *</label>
                <select required>
                    <option value="">اختر المستوى</option>
                    <option value="مبتدئ">مبتدئ</option>
                    <option value="متوسط">متوسط</option>
                    <option value="متقدم">متقدم</option>
                    <option value="ممتاز">ممتاز</option>
                    <option value="لغة أم">لغة أم</option>
                </select>
            </div>
        </div>
    `;

    languagesList.appendChild(languageItem);

    // Add animation
    languageItem.style.opacity = '0';
    languageItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        languageItem.style.transition = 'all 0.3s ease';
        languageItem.style.opacity = '1';
        languageItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeLanguage(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function toggleCurrentJob(checkbox, experienceId) {
    const endDateInput = checkbox.closest('.form-group').querySelector('input[type="month"]');
    if (checkbox.checked) {
        endDateInput.disabled = true;
        endDateInput.value = '';
        endDateInput.placeholder = 'حتى الآن';
        endDateInput.style.backgroundColor = 'var(--bg-secondary)';
    } else {
        endDateInput.disabled = false;
        endDateInput.placeholder = '';
        endDateInput.style.backgroundColor = '';
    }

    // Update experience data
    if (experienceId) {
        updateExperienceData(experienceId);
    }
}

function addSuggestedSkill(skill, type) {
    const inputId = type === 'technical' ? 'technicalSkillInput' : 'softSkillInput';
    const listId = type === 'technical' ? 'technicalSkillsList' : 'softSkillsList';

    addSkillToList(skill, listId);
}

function addSkillToList(skill, listId) {
    const skillsList = document.getElementById(listId);
    if (!skillsList || !skill.trim()) return;

    // Check if skill already exists
    const existingSkills = Array.from(skillsList.querySelectorAll('.skill-tag')).map(tag =>
        tag.querySelector('span').textContent.trim()
    );
    if (existingSkills.includes(skill.trim())) {
        // Show notification that skill already exists
        if (window.app) {
            window.app.showNotification('هذه المهارة موجودة بالفعل', 'warning');
        }
        return;
    }

    const skillTag = document.createElement('div');
    skillTag.className = 'skill-tag';
    skillTag.innerHTML = `
        <span>${skill.trim()}</span>
        <button onclick="removeSkill(this, '${listId}')" class="skill-remove" title="حذف المهارة">
            <i class="fas fa-times"></i>
        </button>
    `;

    skillsList.appendChild(skillTag);

    // Update CV data
    if (window.cvBuilder) {
        const skillType = listId.includes('technical') ? 'technical' : 'soft';
        if (!window.cvBuilder.cvData.skills[skillType]) {
            window.cvBuilder.cvData.skills[skillType] = [];
        }
        window.cvBuilder.cvData.skills[skillType].push(skill.trim());

        // Update progress and save
        window.cvBuilder.updateProgress();
        window.cvBuilder.updatePreview();
        window.cvBuilder.saveToLocalStorage();
    }

    // Add animation
    skillTag.style.opacity = '0';
    skillTag.style.transform = 'scale(0.8)';
    setTimeout(() => {
        skillTag.style.transition = 'all 0.3s ease';
        skillTag.style.opacity = '1';
        skillTag.style.transform = 'scale(1)';
    }, 100);
}

function removeSkill(button, listId) {
    const skillTag = button.closest('.skill-tag');
    const skillText = skillTag.querySelector('span').textContent.trim();

    // Remove from CV data
    if (window.cvBuilder && listId) {
        const skillType = listId.includes('technical') ? 'technical' : 'soft';
        if (window.cvBuilder.cvData.skills[skillType]) {
            window.cvBuilder.cvData.skills[skillType] = window.cvBuilder.cvData.skills[skillType]
                .filter(skill => skill !== skillText);

            // Update progress and save
            window.cvBuilder.updateProgress();
            window.cvBuilder.updatePreview();
            window.cvBuilder.saveToLocalStorage();
        }
    }

    // Remove from DOM with animation
    skillTag.style.transition = 'all 0.3s ease';
    skillTag.style.opacity = '0';
    skillTag.style.transform = 'scale(0.8)';
    setTimeout(() => {
        skillTag.remove();
    }, 300);
}

function setupSkillsInput() {
    const technicalInput = document.getElementById('technicalSkillInput');
    const softInput = document.getElementById('softSkillInput');

    if (technicalInput) {
        // Handle Enter key
        technicalInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const skill = e.target.value.trim();
                if (skill) {
                    addSkillToList(skill, 'technicalSkillsList');
                    e.target.value = '';
                }
            }
        });

        // Handle blur event (when user clicks away)
        technicalInput.addEventListener('blur', (e) => {
            const skill = e.target.value.trim();
            if (skill) {
                addSkillToList(skill, 'technicalSkillsList');
                e.target.value = '';
            }
        });

        // Add placeholder suggestions
        technicalInput.addEventListener('focus', () => {
            technicalInput.placeholder = 'مثل: JavaScript, React, Python - اضغط Enter للإضافة';
        });

        technicalInput.addEventListener('blur', () => {
            technicalInput.placeholder = 'أدخل مهارة تقنية واضغط Enter';
        });
    }

    if (softInput) {
        // Handle Enter key
        softInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const skill = e.target.value.trim();
                if (skill) {
                    addSkillToList(skill, 'softSkillsList');
                    e.target.value = '';
                }
            }
        });

        // Handle blur event
        softInput.addEventListener('blur', (e) => {
            const skill = e.target.value.trim();
            if (skill) {
                addSkillToList(skill, 'softSkillsList');
                e.target.value = '';
            }
        });

        // Add placeholder suggestions
        softInput.addEventListener('focus', () => {
            softInput.placeholder = 'مثل: العمل الجماعي, القيادة, التواصل - اضغط Enter للإضافة';
        });

        softInput.addEventListener('blur', () => {
            softInput.placeholder = 'أدخل مهارة شخصية واضغط Enter';
        });
    }
}

function applySuggestion(suggestionId) {
    const suggestions = {
        'summary1': 'أضف المزيد من التفاصيل حول خبراتك التقنية والأدوات التي تستخدمها',
        'summary2': 'اذكر إنجازاتك الرئيسية بأرقام محددة (مثل: زيادة الإنتاجية بنسبة 30%)',
        'summary3': 'أضف أهدافك المهنية وما تسعى لتحقيقه في المستقبل'
    };

    const suggestion = suggestions[suggestionId];
    if (suggestion && window.aiAssistant) {
        window.aiAssistant.showSuggestion(suggestion, 'tip');
    }
}

function finishCV() {
    // Save CV data
    if (window.cvBuilder) {
        window.cvBuilder.saveToLocalStorage();
    }

    // Show completion message
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-check-circle"></i> تم إنشاء سيرتك الذاتية بنجاح!</h3>
            </div>
            <div class="modal-body">
                <p>تهانينا! لقد أكملت إنشاء سيرتك الذاتية. يمكنك الآن معاينتها وتصديرها.</p>
                <div class="completion-actions">
                    <button class="btn btn-primary" onclick="previewCV()">
                        <i class="fas fa-eye"></i>
                        معاينة السيرة الذاتية
                    </button>
                    <button class="btn btn-outline" onclick="exportCV()">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function zoomIn() {
    const preview = document.querySelector('.cv-preview-document');
    const zoomLevel = document.querySelector('.zoom-level');

    if (preview && zoomLevel) {
        let currentZoom = parseInt(zoomLevel.textContent) || 100;
        currentZoom = Math.min(currentZoom + 10, 150);

        preview.style.transform = `scale(${currentZoom / 100})`;
        zoomLevel.textContent = `${currentZoom}%`;
    }
}

function zoomOut() {
    const preview = document.querySelector('.cv-preview-document');
    const zoomLevel = document.querySelector('.zoom-level');

    if (preview && zoomLevel) {
        let currentZoom = parseInt(zoomLevel.textContent) || 100;
        currentZoom = Math.max(currentZoom - 10, 50);

        preview.style.transform = `scale(${currentZoom / 100})`;
        zoomLevel.textContent = `${currentZoom}%`;
    }
}

// Export functions
function exportToPDF() {
    if (!window.cvPreview) {
        window.cvPreview = new CVPreview();
    }

    // Close export modal
    document.querySelector('.modal')?.remove();

    // Show loading
    showExportLoading('جاري إنشاء ملف PDF...');

    setTimeout(() => {
        if (window.exportToPDF) {
            window.exportToPDF();
        } else {
            hideExportLoading();
            showNotification('وظيفة التصدير غير متاحة حالياً', 'error');
        }
    }, 500);
}

function exportToImage(format) {
    if (!window.cvPreview) {
        window.cvPreview = new CVPreview();
    }

    // Close export modal
    document.querySelector('.modal')?.remove();

    // Show loading
    showExportLoading(`جاري إنشاء صورة ${format.toUpperCase()}...`);

    setTimeout(() => {
        if (window.exportToImage) {
            window.exportToImage(format);
        } else {
            hideExportLoading();
            showNotification('وظيفة التصدير غير متاحة حالياً', 'error');
        }
    }, 500);
}

function generateQRCode() {
    // Close export modal
    document.querySelector('.modal')?.remove();

    if (!window.cvBuilder.cvData.personal.email && !window.cvBuilder.cvData.personal.phone) {
        showNotification('يرجى إضافة معلومات الاتصال أولاً', 'warning');
        return;
    }

    if (window.generateQRCode) {
        window.generateQRCode();
    } else {
        showNotification('وظيفة إنشاء رمز QR غير متاحة حالياً', 'error');
    }
}

function showExportLoading(message) {
    const loading = document.createElement('div');
    loading.id = 'exportLoading';
    loading.className = 'export-loading';
    loading.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>${message}</p>
        </div>
    `;
    document.body.appendChild(loading);
}

function hideExportLoading() {
    const loading = document.getElementById('exportLoading');
    if (loading) {
        loading.remove();
    }
}

function showNotification(message, type = 'info') {
    if (window.app && window.app.showNotification) {
        window.app.showNotification(message, type);
    } else {
        // Fallback notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--bg-card);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            z-index: 9999;
            max-width: 300px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize CV Builder when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvBuilder = new CVBuilder();

    // Setup skills input after DOM is loaded
    setTimeout(() => {
        setupSkillsInput();
    }, 1000);

    // Initialize CV Preview for export functionality
    setTimeout(() => {
        if (typeof CVPreview !== 'undefined') {
            window.cvPreview = new CVPreview();
        }
    }, 2000);
});
