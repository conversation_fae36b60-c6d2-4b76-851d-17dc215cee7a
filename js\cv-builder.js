// ===== CV Builder JavaScript =====

class CVBuilder {
    constructor() {
        this.currentSection = 'personal';
        this.cvData = {
            personal: {},
            summary: '',
            experience: [],
            education: [],
            skills: [],
            languages: [],
            projects: [],
            certifications: []
        };
        this.currentTemplate = 1;
        this.isPreviewMode = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSavedData();
        this.updateProgress();
        this.initializePhotoUpload();
        this.setupAutoSave();
    }
    
    setupEventListeners() {
        // Section navigation
        document.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = item.dataset.section;
                this.switchSection(section);
            });
        });
        
        // Form inputs
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.handleInputChange(e);
            }
        });
        
        // Photo upload
        const photoInput = document.getElementById('photoInput');
        if (photoInput) {
            photoInput.addEventListener('change', (e) => this.handlePhotoUpload(e));
        }
        
        // Real-time preview updates
        this.setupRealTimePreview();
    }
    
    switchSection(sectionName) {
        // Update sidebar
        document.querySelectorAll('.section-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // Update editor content
        document.querySelectorAll('.editor-section').forEach(section => {
            section.style.display = 'none';
        });
        
        const targetSection = document.getElementById(`${sectionName}-section`);
        if (targetSection) {
            targetSection.style.display = 'block';
        } else {
            this.createSectionContent(sectionName);
        }
        
        this.currentSection = sectionName;
        this.updateProgress();
    }
    
    createSectionContent(sectionName) {
        const editor = document.querySelector('.cv-editor');
        let sectionHTML = '';
        
        switch (sectionName) {
            case 'summary':
                sectionHTML = this.createSummarySection();
                break;
            case 'experience':
                sectionHTML = this.createExperienceSection();
                break;
            case 'education':
                sectionHTML = this.createEducationSection();
                break;
            case 'skills':
                sectionHTML = this.createSkillsSection();
                break;
            case 'languages':
                sectionHTML = this.createLanguagesSection();
                break;
        }
        
        if (sectionHTML) {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'editor-section';
            sectionDiv.id = `${sectionName}-section`;
            sectionDiv.innerHTML = sectionHTML;
            editor.appendChild(sectionDiv);
        }
    }
    
    createSummarySection() {
        return `
            <div class="section-header">
                <h2>الملخص المهني</h2>
                <p>اكتب نبذة مختصرة عن خبراتك وأهدافك المهنية</p>
            </div>
            
            <div class="form-group">
                <label>الملخص المهني *</label>
                <textarea id="professionalSummary" rows="6" 
                    placeholder="مثال: مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة باستخدام React و Node.js. أسعى لتطوير حلول تقنية مبتكرة تساهم في نمو الشركة وتحسين تجربة المستخدم."
                    data-field="summary"></textarea>
                <div class="character-count">
                    <span id="summaryCount">0</span> / 500 حرف
                </div>
            </div>
            
            <div class="ai-suggestions-box">
                <h4><i class="fas fa-robot"></i> اقتراحات المساعد الذكي</h4>
                <div class="suggestions-list">
                    <button class="suggestion-btn" onclick="applySuggestion('summary1')">
                        أضف المزيد عن خبراتك التقنية
                    </button>
                    <button class="suggestion-btn" onclick="applySuggestion('summary2')">
                        اذكر إنجازاتك الرئيسية
                    </button>
                    <button class="suggestion-btn" onclick="applySuggestion('summary3')">
                        أضف أهدافك المهنية
                    </button>
                </div>
            </div>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: الخبرات العملية
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createExperienceSection() {
        return `
            <div class="section-header">
                <h2>الخبرات العملية</h2>
                <p>أضف تاريخك المهني والوظائف التي شغلتها</p>
            </div>
            
            <div class="experience-list" id="experienceList">
                <!-- Experience items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addExperience()">
                <i class="fas fa-plus"></i>
                إضافة خبرة عملية
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: التعليم
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createEducationSection() {
        return `
            <div class="section-header">
                <h2>التعليم</h2>
                <p>أضف مؤهلاتك الأكاديمية والشهادات</p>
            </div>
            
            <div class="education-list" id="educationList">
                <!-- Education items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addEducation()">
                <i class="fas fa-plus"></i>
                إضافة مؤهل أكاديمي
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: المهارات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createSkillsSection() {
        return `
            <div class="section-header">
                <h2>المهارات</h2>
                <p>أضف مهاراتك التقنية والشخصية</p>
            </div>
            
            <div class="skills-categories">
                <div class="skill-category">
                    <h4>المهارات التقنية</h4>
                    <div class="skills-input">
                        <input type="text" id="technicalSkillInput" placeholder="أدخل مهارة تقنية واضغط Enter">
                        <div class="skills-list" id="technicalSkillsList"></div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h4>المهارات الشخصية</h4>
                    <div class="skills-input">
                        <input type="text" id="softSkillInput" placeholder="أدخل مهارة شخصية واضغط Enter">
                        <div class="skills-list" id="softSkillsList"></div>
                    </div>
                </div>
            </div>
            
            <div class="ai-suggestions-box">
                <h4><i class="fas fa-robot"></i> مهارات مقترحة</h4>
                <div class="suggested-skills">
                    <button class="skill-suggestion" onclick="addSuggestedSkill('JavaScript', 'technical')">JavaScript</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('React', 'technical')">React</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('Node.js', 'technical')">Node.js</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('العمل الجماعي', 'soft')">العمل الجماعي</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('القيادة', 'soft')">القيادة</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('التواصل', 'soft')">التواصل</button>
                </div>
            </div>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: اللغات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createLanguagesSection() {
        return `
            <div class="section-header">
                <h2>اللغات</h2>
                <p>أضف اللغات التي تتقنها ومستوى إتقانك لكل منها</p>
            </div>
            
            <div class="languages-list" id="languagesList">
                <!-- Language items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addLanguage()">
                <i class="fas fa-plus"></i>
                إضافة لغة
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="finishCV()">
                    إنهاء وحفظ
                    <i class="fas fa-check"></i>
                </button>
            </div>
        `;
    }
    
    handleInputChange(e) {
        const field = e.target.dataset.field || e.target.id;
        const value = e.target.value;
        
        // Update CV data
        if (this.currentSection === 'personal') {
            this.cvData.personal[field] = value;
        } else if (field === 'summary') {
            this.cvData.summary = value;
        }
        
        // Update character count for summary
        if (field === 'professionalSummary') {
            const countElement = document.getElementById('summaryCount');
            if (countElement) {
                countElement.textContent = value.length;
            }
        }
        
        // Update preview
        this.updatePreview();
        
        // Mark section as completed
        this.markSectionCompleted(this.currentSection);
    }
    
    handlePhotoUpload(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
                const photoPreview = document.getElementById('photoPreview');
                photoPreview.innerHTML = `<img src="${event.target.result}" alt="Profile Photo">`;
                
                // Update CV data
                this.cvData.personal.photo = event.target.result;
                this.updatePreview();
            };
            reader.readAsDataURL(file);
        }
    }
    
    initializePhotoUpload() {
        // Drag and drop functionality
        const photoPreview = document.getElementById('photoPreview');
        if (photoPreview) {
            photoPreview.addEventListener('dragover', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--primary-color)';
            });
            
            photoPreview.addEventListener('dragleave', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--bg-secondary)';
            });
            
            photoPreview.addEventListener('drop', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--bg-secondary)';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const photoInput = document.getElementById('photoInput');
                    photoInput.files = files;
                    this.handlePhotoUpload({ target: photoInput });
                }
            });
        }
    }
    
    updatePreview() {
        // Update CV preview in real-time
        const preview = document.getElementById('cvPreview');
        if (preview) {
            this.renderCVPreview();
        }
    }
    
    renderCVPreview() {
        const data = this.cvData;
        const preview = document.querySelector('.cv-page');
        
        // Update basic info
        const nameElement = preview.querySelector('.cv-name');
        const titleElement = preview.querySelector('.cv-title');
        const photoElement = preview.querySelector('.cv-photo');
        
        if (nameElement) nameElement.textContent = data.personal.fullName || 'اسمك هنا';
        if (titleElement) titleElement.textContent = data.personal.jobTitle || 'المسمى الوظيفي';
        
        if (photoElement && data.personal.photo) {
            photoElement.innerHTML = `<img src="${data.personal.photo}" alt="Profile Photo">`;
        }
        
        // Update contact info
        this.updateContactInfo();
        
        // Update summary
        this.updateSummaryPreview();
    }
    
    updateContactInfo() {
        const contactContainer = document.querySelector('.cv-contact');
        if (!contactContainer) return;
        
        const data = this.cvData.personal;
        let contactHTML = '';
        
        if (data.email) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${data.email}</span>
                </div>
            `;
        }
        
        if (data.phone) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${data.phone}</span>
                </div>
            `;
        }
        
        if (data.city) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${data.city}</span>
                </div>
            `;
        }
        
        contactContainer.innerHTML = contactHTML;
    }
    
    updateSummaryPreview() {
        const summarySection = document.querySelector('.cv-content .cv-section');
        if (summarySection && this.cvData.summary) {
            summarySection.querySelector('p').textContent = this.cvData.summary;
        }
    }
    
    markSectionCompleted(sectionName) {
        const sectionItem = document.querySelector(`[data-section="${sectionName}"]`);
        const statusIcon = sectionItem.querySelector('.section-status i');
        
        if (this.isSectionComplete(sectionName)) {
            statusIcon.className = 'fas fa-check-circle';
            statusIcon.style.color = 'var(--accent-color)';
        }
    }
    
    isSectionComplete(sectionName) {
        switch (sectionName) {
            case 'personal':
                return this.cvData.personal.fullName && 
                       this.cvData.personal.jobTitle && 
                       this.cvData.personal.email && 
                       this.cvData.personal.phone;
            case 'summary':
                return this.cvData.summary && this.cvData.summary.length > 50;
            default:
                return false;
        }
    }
    
    updateProgress() {
        const completedSections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages']
            .filter(section => this.isSectionComplete(section)).length;
        
        const totalSections = 6;
        const progress = (completedSections / totalSections) * 100;
        
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        if (progressFill) progressFill.style.width = `${progress}%`;
        if (progressText) progressText.textContent = `${Math.round(progress)}% مكتمل`;
    }
    
    setupAutoSave() {
        setInterval(() => {
            this.saveToLocalStorage();
        }, 30000); // Auto-save every 30 seconds
    }
    
    saveToLocalStorage() {
        localStorage.setItem('elashrafy_cv_data', JSON.stringify(this.cvData));
        localStorage.setItem('elashrafy_cv_current_section', this.currentSection);
    }
    
    loadSavedData() {
        const savedData = localStorage.getItem('elashrafy_cv_data');
        const savedSection = localStorage.getItem('elashrafy_cv_current_section');
        
        if (savedData) {
            this.cvData = JSON.parse(savedData);
            this.populateForm();
        }
        
        if (savedSection) {
            this.switchSection(savedSection);
        }
    }
    
    populateForm() {
        // Populate personal information
        Object.keys(this.cvData.personal).forEach(key => {
            const input = document.getElementById(key);
            if (input) {
                input.value = this.cvData.personal[key];
            }
        });
        
        // Update preview
        this.updatePreview();
    }
}

// Global functions
function nextSection() {
    const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
    const currentIndex = sections.indexOf(window.cvBuilder.currentSection);
    if (currentIndex < sections.length - 1) {
        window.cvBuilder.switchSection(sections[currentIndex + 1]);
    }
}

function previousSection() {
    const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
    const currentIndex = sections.indexOf(window.cvBuilder.currentSection);
    if (currentIndex > 0) {
        window.cvBuilder.switchSection(sections[currentIndex - 1]);
    }
}

function removePhoto() {
    const photoPreview = document.getElementById('photoPreview');
    photoPreview.innerHTML = '<i class="fas fa-user"></i>';
    
    window.cvBuilder.cvData.personal.photo = null;
    window.cvBuilder.updatePreview();
}

function saveCV() {
    window.cvBuilder.saveToLocalStorage();
    // Show success notification
    if (window.app) {
        window.app.showNotification('تم حفظ السيرة الذاتية بنجاح', 'success');
    }
}

function previewCV() {
    // Open preview in new window or modal
    window.open('cv-preview.html', '_blank');
}

function exportCV() {
    // Show export options modal
    console.log('Export CV');
}

// Additional CV Builder Functions

function addExperience() {
    const experienceList = document.getElementById('experienceList');
    const experienceId = Date.now();

    const experienceItem = document.createElement('div');
    experienceItem.className = 'experience-item';
    experienceItem.dataset.id = experienceId;

    experienceItem.innerHTML = `
        <div class="item-header">
            <h4>خبرة عملية جديدة</h4>
            <button class="btn-remove" onclick="removeExperience(${experienceId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>المسمى الوظيفي *</label>
                <input type="text" placeholder="مثل: مطور ويب أول" required>
            </div>
            <div class="form-group">
                <label>اسم الشركة *</label>
                <input type="text" placeholder="مثل: شركة التقنية المتقدمة" required>
            </div>
            <div class="form-group">
                <label>تاريخ البداية *</label>
                <input type="month" required>
            </div>
            <div class="form-group">
                <label>تاريخ النهاية</label>
                <input type="month">
                <label class="checkbox-label">
                    <input type="checkbox" onchange="toggleCurrentJob(this)">
                    <span>أعمل حالياً في هذه الوظيفة</span>
                </label>
            </div>
            <div class="form-group full-width">
                <label>وصف المهام والإنجازات</label>
                <textarea rows="4" placeholder="اكتب وصفاً مفصلاً عن مهامك وإنجازاتك في هذه الوظيفة..."></textarea>
            </div>
        </div>
    `;

    experienceList.appendChild(experienceItem);

    // Add animation
    experienceItem.style.opacity = '0';
    experienceItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        experienceItem.style.transition = 'all 0.3s ease';
        experienceItem.style.opacity = '1';
        experienceItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeExperience(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function addEducation() {
    const educationList = document.getElementById('educationList');
    const educationId = Date.now();

    const educationItem = document.createElement('div');
    educationItem.className = 'education-item';
    educationItem.dataset.id = educationId;

    educationItem.innerHTML = `
        <div class="item-header">
            <h4>مؤهل أكاديمي جديد</h4>
            <button class="btn-remove" onclick="removeEducation(${educationId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>الدرجة العلمية *</label>
                <input type="text" placeholder="مثل: بكالوريوس علوم الحاسب" required>
            </div>
            <div class="form-group">
                <label>اسم الجامعة/المؤسسة *</label>
                <input type="text" placeholder="مثل: جامعة الملك سعود" required>
            </div>
            <div class="form-group">
                <label>سنة التخرج *</label>
                <input type="number" min="1950" max="2030" placeholder="2020" required>
            </div>
            <div class="form-group">
                <label>المعدل التراكمي</label>
                <input type="text" placeholder="3.8 من 4.0">
            </div>
            <div class="form-group full-width">
                <label>تفاصيل إضافية</label>
                <textarea rows="3" placeholder="اذكر أي تفاصيل إضافية مثل التخصص الفرعي، المشاريع المميزة، إلخ..."></textarea>
            </div>
        </div>
    `;

    educationList.appendChild(educationItem);

    // Add animation
    educationItem.style.opacity = '0';
    educationItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        educationItem.style.transition = 'all 0.3s ease';
        educationItem.style.opacity = '1';
        educationItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeEducation(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function addLanguage() {
    const languagesList = document.getElementById('languagesList');
    const languageId = Date.now();

    const languageItem = document.createElement('div');
    languageItem.className = 'language-item';
    languageItem.dataset.id = languageId;

    languageItem.innerHTML = `
        <div class="item-header">
            <h4>لغة جديدة</h4>
            <button class="btn-remove" onclick="removeLanguage(${languageId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>اسم اللغة *</label>
                <input type="text" placeholder="مثل: الإنجليزية" required>
            </div>
            <div class="form-group">
                <label>مستوى الإتقان *</label>
                <select required>
                    <option value="">اختر المستوى</option>
                    <option value="مبتدئ">مبتدئ</option>
                    <option value="متوسط">متوسط</option>
                    <option value="متقدم">متقدم</option>
                    <option value="ممتاز">ممتاز</option>
                    <option value="لغة أم">لغة أم</option>
                </select>
            </div>
        </div>
    `;

    languagesList.appendChild(languageItem);

    // Add animation
    languageItem.style.opacity = '0';
    languageItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        languageItem.style.transition = 'all 0.3s ease';
        languageItem.style.opacity = '1';
        languageItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeLanguage(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function toggleCurrentJob(checkbox) {
    const endDateInput = checkbox.closest('.form-group').querySelector('input[type="month"]');
    if (checkbox.checked) {
        endDateInput.disabled = true;
        endDateInput.value = '';
        endDateInput.placeholder = 'حتى الآن';
    } else {
        endDateInput.disabled = false;
        endDateInput.placeholder = '';
    }
}

function addSuggestedSkill(skill, type) {
    const inputId = type === 'technical' ? 'technicalSkillInput' : 'softSkillInput';
    const listId = type === 'technical' ? 'technicalSkillsList' : 'softSkillsList';

    addSkillToList(skill, listId);
}

function addSkillToList(skill, listId) {
    const skillsList = document.getElementById(listId);
    if (!skillsList) return;

    // Check if skill already exists
    const existingSkills = Array.from(skillsList.querySelectorAll('.skill-tag')).map(tag => tag.textContent.trim());
    if (existingSkills.includes(skill)) {
        return;
    }

    const skillTag = document.createElement('div');
    skillTag.className = 'skill-tag';
    skillTag.innerHTML = `
        <span>${skill}</span>
        <button onclick="removeSkill(this)" class="skill-remove">
            <i class="fas fa-times"></i>
        </button>
    `;

    skillsList.appendChild(skillTag);

    // Add animation
    skillTag.style.opacity = '0';
    skillTag.style.transform = 'scale(0.8)';
    setTimeout(() => {
        skillTag.style.transition = 'all 0.3s ease';
        skillTag.style.opacity = '1';
        skillTag.style.transform = 'scale(1)';
    }, 100);
}

function removeSkill(button) {
    const skillTag = button.closest('.skill-tag');
    skillTag.style.transition = 'all 0.3s ease';
    skillTag.style.opacity = '0';
    skillTag.style.transform = 'scale(0.8)';
    setTimeout(() => {
        skillTag.remove();
    }, 300);
}

function setupSkillsInput() {
    const technicalInput = document.getElementById('technicalSkillInput');
    const softInput = document.getElementById('softSkillInput');

    if (technicalInput) {
        technicalInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const skill = e.target.value.trim();
                if (skill) {
                    addSkillToList(skill, 'technicalSkillsList');
                    e.target.value = '';
                }
            }
        });
    }

    if (softInput) {
        softInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const skill = e.target.value.trim();
                if (skill) {
                    addSkillToList(skill, 'softSkillsList');
                    e.target.value = '';
                }
            }
        });
    }
}

function applySuggestion(suggestionId) {
    const suggestions = {
        'summary1': 'أضف المزيد من التفاصيل حول خبراتك التقنية والأدوات التي تستخدمها',
        'summary2': 'اذكر إنجازاتك الرئيسية بأرقام محددة (مثل: زيادة الإنتاجية بنسبة 30%)',
        'summary3': 'أضف أهدافك المهنية وما تسعى لتحقيقه في المستقبل'
    };

    const suggestion = suggestions[suggestionId];
    if (suggestion && window.aiAssistant) {
        window.aiAssistant.showSuggestion(suggestion, 'tip');
    }
}

function finishCV() {
    // Save CV data
    if (window.cvBuilder) {
        window.cvBuilder.saveToLocalStorage();
    }

    // Show completion message
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-check-circle"></i> تم إنشاء سيرتك الذاتية بنجاح!</h3>
            </div>
            <div class="modal-body">
                <p>تهانينا! لقد أكملت إنشاء سيرتك الذاتية. يمكنك الآن معاينتها وتصديرها.</p>
                <div class="completion-actions">
                    <button class="btn btn-primary" onclick="previewCV()">
                        <i class="fas fa-eye"></i>
                        معاينة السيرة الذاتية
                    </button>
                    <button class="btn btn-outline" onclick="exportCV()">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function zoomIn() {
    const preview = document.querySelector('.cv-preview-document');
    const zoomLevel = document.querySelector('.zoom-level');

    if (preview && zoomLevel) {
        let currentZoom = parseInt(zoomLevel.textContent) || 100;
        currentZoom = Math.min(currentZoom + 10, 150);

        preview.style.transform = `scale(${currentZoom / 100})`;
        zoomLevel.textContent = `${currentZoom}%`;
    }
}

function zoomOut() {
    const preview = document.querySelector('.cv-preview-document');
    const zoomLevel = document.querySelector('.zoom-level');

    if (preview && zoomLevel) {
        let currentZoom = parseInt(zoomLevel.textContent) || 100;
        currentZoom = Math.max(currentZoom - 10, 50);

        preview.style.transform = `scale(${currentZoom / 100})`;
        zoomLevel.textContent = `${currentZoom}%`;
    }
}

// Initialize CV Builder when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvBuilder = new CVBuilder();

    // Setup skills input after DOM is loaded
    setTimeout(() => {
        setupSkillsInput();
    }, 1000);
});
