// ===== CV Builder JavaScript - Fixed Version =====

class CVBuilder {
    constructor() {
        this.currentSection = 'personal';
        this.selectedTemplate = null;
        this.isInitialized = false;
        this.cvData = {
            personal: {
                fullName: '',
                jobTitle: '',
                email: '',
                phone: '',
                city: '',
                website: '',
                linkedin: '',
                github: '',
                photo: null
            },
            summary: '',
            experience: [],
            education: [],
            skills: {
                technical: [],
                soft: []
            },
            languages: []
        };

        this.init();
    }
    
    init() {
        try {
            console.log('🚀 تهيئة منشئ السيرة الذاتية...');

            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.initializeComponents());
            } else {
                this.initializeComponents();
            }
        } catch (error) {
            console.error('❌ خطأ في تهيئة منشئ السيرة الذاتية:', error);
            this.showError('حدث خطأ في تحميل التطبيق. يرجى إعادة تحميل الصفحة.');
        }
    }

    initializeComponents() {
        try {
            this.loadSelectedTemplate();
            this.setupEventListeners();
            this.loadSavedData();
            this.createAllSections();
            this.updateProgress();
            this.initializePhotoUpload();
            this.setupAutoSave();
            this.updatePreview();
            this.isInitialized = true;

            console.log('✅ تم تهيئة منشئ السيرة الذاتية بنجاح');
            this.showNotification('مرحباً! ابدأ في إنشاء سيرتك الذاتية', 'success');
        } catch (error) {
            console.error('❌ خطأ في تهيئة المكونات:', error);
            this.showError('حدث خطأ في تحميل المكونات. يرجى إعادة تحميل الصفحة.');
        }
    }
    
    setupEventListeners() {
        // Section navigation
        document.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = item.dataset.section;
                this.switchSection(section);
            });
        });
        
        // Form inputs
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.handleInputChange(e);
            }
        });
        
        // Photo upload
        const photoInput = document.getElementById('photoInput');
        if (photoInput) {
            photoInput.addEventListener('change', (e) => this.handlePhotoUpload(e));
        }
        
        // Auto-save setup
        this.setupAutoSave();

        console.log('✅ تم تهيئة مستمعي الأحداث بنجاح');
    }

    setupAutoSave() {
        // Auto-save every 30 seconds
        setInterval(() => {
            try {
                this.saveToLocalStorage();
            } catch (error) {
                console.error('خطأ في الحفظ التلقائي:', error);
            }
        }, 30000);
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.getAttribute('placeholder') || field.id;

        // Remove existing error
        const existingError = field.parentElement.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        field.classList.remove('error');

        // Check if required field is empty
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(field, `${fieldName} مطلوب`);
            return false;
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
                return false;
            }
        }

        // Phone validation
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
                return false;
            }
        }

        // URL validation
        if (field.type === 'url' && value) {
            try {
                new URL(value);
            } catch {
                this.showFieldError(field, 'يرجى إدخال رابط صحيح');
                return false;
            }
        }

        return true;
    }

    showFieldError(field, message) {
        field.classList.add('error');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;

        field.parentElement.appendChild(errorDiv);
    }

    createAllSections() {
        // Create all sections at once to avoid issues
        const sections = ['summary', 'experience', 'education', 'skills', 'languages'];

        sections.forEach(sectionName => {
            const existingSection = document.getElementById(`${sectionName}-section`);
            if (!existingSection) {
                this.createSectionContent(sectionName);
            }
        });
    }
    
    switchSection(sectionName) {
        try {
            // Validate section name
            const validSections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
            if (!validSections.includes(sectionName)) {
                console.error('قسم غير صحيح:', sectionName);
                return;
            }

            // Update sidebar
            document.querySelectorAll('.section-item').forEach(item => {
                item.classList.remove('active');
            });

            const sidebarItem = document.querySelector(`[data-section="${sectionName}"]`);
            if (sidebarItem) {
                sidebarItem.classList.add('active');
            }

            // Update editor content
            document.querySelectorAll('.editor-section').forEach(section => {
                section.style.display = 'none';
            });

            const targetSection = document.getElementById(`${sectionName}-section`);
            if (targetSection) {
                targetSection.style.display = 'block';
            } else {
                this.createSectionContent(sectionName);
            }

            this.currentSection = sectionName;
            this.updateProgress();

            // Scroll to top of editor
            const editor = document.querySelector('.cv-editor');
            if (editor) {
                editor.scrollTop = 0;
            }

            console.log(`✅ تم التبديل إلى قسم: ${sectionName}`);
        } catch (error) {
            console.error('خطأ في تبديل القسم:', error);
            this.showError('حدث خطأ في التنقل بين الأقسام');
        }
    }

    nextSection() {
        const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
        const currentIndex = sections.indexOf(this.currentSection);

        if (currentIndex < sections.length - 1) {
            this.switchSection(sections[currentIndex + 1]);
        } else {
            this.showNotification('لقد وصلت إلى آخر قسم', 'info');
        }
    }

    prevSection() {
        const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
        const currentIndex = sections.indexOf(this.currentSection);

        if (currentIndex > 0) {
            this.switchSection(sections[currentIndex - 1]);
        } else {
            this.showNotification('أنت في أول قسم', 'info');
        }
    }

    updateProgress() {
        try {
            const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
            let completedSections = 0;

            // Check personal section
            if (this.cvData.personal.fullName && this.cvData.personal.email) {
                completedSections++;
            }

            // Check summary section
            if (this.cvData.summary && this.cvData.summary.length >= 50) {
                completedSections++;
            }

            // Check experience section
            if (this.cvData.experience && this.cvData.experience.length > 0) {
                completedSections++;
            }

            // Check education section
            if (this.cvData.education && this.cvData.education.length > 0) {
                completedSections++;
            }

            // Check skills section
            if (this.cvData.skills && (this.cvData.skills.technical?.length > 0 || this.cvData.skills.soft?.length > 0)) {
                completedSections++;
            }

            // Check languages section
            if (this.cvData.languages && this.cvData.languages.length > 0) {
                completedSections++;
            }

            const progressPercentage = Math.round((completedSections / sections.length) * 100);

            // Update progress bar
            const progressBar = document.querySelector('.progress-fill');
            const progressText = document.querySelector('.progress-text');

            if (progressBar) {
                progressBar.style.width = `${progressPercentage}%`;
            }

            if (progressText) {
                progressText.textContent = `${progressPercentage}% مكتمل`;
            }

            // Update section indicators
            this.updateSectionIndicators(completedSections, sections);

        } catch (error) {
            console.error('خطأ في تحديث التقدم:', error);
        }
    }

    updateSectionIndicators(completedSections, sections) {
        sections.forEach((section, index) => {
            const sectionItem = document.querySelector(`[data-section="${section}"]`);
            if (sectionItem) {
                const indicator = sectionItem.querySelector('.section-indicator');
                if (indicator) {
                    if (index < completedSections) {
                        indicator.innerHTML = '<i class="fas fa-check"></i>';
                        indicator.classList.add('completed');
                    } else {
                        indicator.innerHTML = index + 1;
                        indicator.classList.remove('completed');
                    }
                }
            }
        });
    }

    markSectionCompleted(sectionName) {
        const sectionItem = document.querySelector(`[data-section="${sectionName}"]`);
        if (sectionItem) {
            sectionItem.classList.add('completed');
        }
    }
    
    createSectionContent(sectionName) {
        const editor = document.querySelector('.cv-editor');
        let sectionHTML = '';
        
        switch (sectionName) {
            case 'summary':
                sectionHTML = this.createSummarySection();
                break;
            case 'experience':
                sectionHTML = this.createExperienceSection();
                break;
            case 'education':
                sectionHTML = this.createEducationSection();
                break;
            case 'skills':
                sectionHTML = this.createSkillsSection();
                break;
            case 'languages':
                sectionHTML = this.createLanguagesSection();
                break;
        }
        
        if (sectionHTML) {
            const sectionDiv = document.createElement('div');
            sectionDiv.className = 'editor-section';
            sectionDiv.id = `${sectionName}-section`;
            sectionDiv.innerHTML = sectionHTML;
            editor.appendChild(sectionDiv);
        }
    }
    
    createSummarySection() {
        return `
            <div class="section-header">
                <h2>الملخص المهني</h2>
                <p>اكتب نبذة مختصرة عن خبراتك وأهدافك المهنية</p>
            </div>
            
            <div class="form-group">
                <label>الملخص المهني *</label>
                <textarea id="professionalSummary" rows="6" 
                    placeholder="مثال: مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة باستخدام React و Node.js. أسعى لتطوير حلول تقنية مبتكرة تساهم في نمو الشركة وتحسين تجربة المستخدم."
                    data-field="summary"></textarea>
                <div class="character-count">
                    <span id="summaryCount">0</span> / 500 حرف
                </div>
            </div>
            
            <div class="ai-suggestions-box">
                <h4><i class="fas fa-robot"></i> اقتراحات المساعد الذكي</h4>
                <div class="suggestions-list">
                    <button class="suggestion-btn" onclick="applySuggestion('summary1')">
                        أضف المزيد عن خبراتك التقنية
                    </button>
                    <button class="suggestion-btn" onclick="applySuggestion('summary2')">
                        اذكر إنجازاتك الرئيسية
                    </button>
                    <button class="suggestion-btn" onclick="applySuggestion('summary3')">
                        أضف أهدافك المهنية
                    </button>
                </div>
            </div>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: الخبرات العملية
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createExperienceSection() {
        return `
            <div class="section-header">
                <h2>الخبرات العملية</h2>
                <p>أضف تاريخك المهني والوظائف التي شغلتها</p>
            </div>
            
            <div class="experience-list" id="experienceList">
                <!-- Experience items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addExperience()">
                <i class="fas fa-plus"></i>
                إضافة خبرة عملية
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: التعليم
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createEducationSection() {
        return `
            <div class="section-header">
                <h2>التعليم</h2>
                <p>أضف مؤهلاتك الأكاديمية والشهادات</p>
            </div>
            
            <div class="education-list" id="educationList">
                <!-- Education items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addEducation()">
                <i class="fas fa-plus"></i>
                إضافة مؤهل أكاديمي
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: المهارات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createSkillsSection() {
        return `
            <div class="section-header">
                <h2>المهارات</h2>
                <p>أضف مهاراتك التقنية والشخصية</p>
            </div>
            
            <div class="skills-categories">
                <div class="skill-category">
                    <h4>المهارات التقنية</h4>
                    <div class="skills-input">
                        <input type="text" id="technicalSkillInput" placeholder="أدخل مهارة تقنية واضغط Enter">
                        <div class="skills-list" id="technicalSkillsList"></div>
                    </div>
                </div>
                
                <div class="skill-category">
                    <h4>المهارات الشخصية</h4>
                    <div class="skills-input">
                        <input type="text" id="softSkillInput" placeholder="أدخل مهارة شخصية واضغط Enter">
                        <div class="skills-list" id="softSkillsList"></div>
                    </div>
                </div>
            </div>
            
            <div class="ai-suggestions-box">
                <h4><i class="fas fa-robot"></i> مهارات مقترحة</h4>
                <div class="suggested-skills">
                    <button class="skill-suggestion" onclick="addSuggestedSkill('JavaScript', 'technical')">JavaScript</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('React', 'technical')">React</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('Node.js', 'technical')">Node.js</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('العمل الجماعي', 'soft')">العمل الجماعي</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('القيادة', 'soft')">القيادة</button>
                    <button class="skill-suggestion" onclick="addSuggestedSkill('التواصل', 'soft')">التواصل</button>
                </div>
            </div>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="nextSection()">
                    التالي: اللغات
                    <i class="fas fa-arrow-left"></i>
                </button>
            </div>
        `;
    }
    
    createLanguagesSection() {
        return `
            <div class="section-header">
                <h2>اللغات</h2>
                <p>أضف اللغات التي تتقنها ومستوى إتقانك لكل منها</p>
            </div>
            
            <div class="languages-list" id="languagesList">
                <!-- Language items will be added here -->
            </div>
            
            <button class="btn btn-outline btn-add" onclick="addLanguage()">
                <i class="fas fa-plus"></i>
                إضافة لغة
            </button>
            
            <div class="section-actions">
                <button class="btn btn-outline" onclick="previousSection()">
                    <i class="fas fa-arrow-right"></i>
                    السابق
                </button>
                <button class="btn btn-primary" onclick="finishCV()">
                    إنهاء وحفظ
                    <i class="fas fa-check"></i>
                </button>
            </div>
        `;
    }
    
    handleInputChange(e) {
        const field = e.target.dataset.field || e.target.id;
        const value = e.target.value;

        // Update CV data based on current section
        if (this.currentSection === 'personal') {
            this.cvData.personal[field] = value;
        } else if (field === 'professionalSummary' || field === 'summary') {
            this.cvData.summary = value;
        }

        // Update character count for summary
        if (field === 'professionalSummary') {
            const countElement = document.getElementById('summaryCount');
            if (countElement) {
                countElement.textContent = value.length;

                // Change color based on length
                if (value.length < 50) {
                    countElement.style.color = 'var(--danger-color)';
                } else if (value.length > 500) {
                    countElement.style.color = 'var(--warning-color)';
                } else {
                    countElement.style.color = 'var(--success-color)';
                }
            }
        }

        // Update preview in real-time
        this.updatePreview();

        // Mark section as completed
        this.markSectionCompleted(this.currentSection);

        // Auto-save
        this.saveToLocalStorage();

        // Trigger custom event for other components
        document.dispatchEvent(new CustomEvent('cvDataUpdated', {
            detail: this.cvData
        }));
    }
    
    handlePhotoUpload(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
                const photoPreview = document.getElementById('photoPreview');
                photoPreview.innerHTML = `<img src="${event.target.result}" alt="Profile Photo">`;
                
                // Update CV data
                this.cvData.personal.photo = event.target.result;
                this.updatePreview();
            };
            reader.readAsDataURL(file);
        }
    }
    
    initializePhotoUpload() {
        // Drag and drop functionality
        const photoPreview = document.getElementById('photoPreview');
        if (photoPreview) {
            photoPreview.addEventListener('dragover', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--primary-color)';
            });
            
            photoPreview.addEventListener('dragleave', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--bg-secondary)';
            });
            
            photoPreview.addEventListener('drop', (e) => {
                e.preventDefault();
                photoPreview.style.borderColor = 'var(--bg-secondary)';
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const photoInput = document.getElementById('photoInput');
                    photoInput.files = files;
                    this.handlePhotoUpload({ target: photoInput });
                }
            });
        }
    }
    
    updatePreview() {
        // Update CV preview in real-time
        const preview = document.getElementById('cvPreview');
        if (preview) {
            this.renderCVPreview();
        }
    }
    
    renderCVPreview() {
        const data = this.cvData;
        const preview = document.querySelector('.cv-page');

        if (!preview) return;

        // Update basic info
        const nameElement = preview.querySelector('.cv-name');
        const titleElement = preview.querySelector('.cv-title');
        const photoElement = preview.querySelector('.cv-photo');

        if (nameElement) {
            nameElement.textContent = data.personal.fullName || 'اسمك هنا';
            nameElement.style.opacity = data.personal.fullName ? '1' : '0.5';
        }

        if (titleElement) {
            titleElement.textContent = data.personal.jobTitle || 'المسمى الوظيفي';
            titleElement.style.opacity = data.personal.jobTitle ? '1' : '0.5';
        }

        if (photoElement) {
            if (data.personal.photo) {
                photoElement.innerHTML = `<img src="${data.personal.photo}" alt="Profile Photo" style="width: 100%; height: 100%; object-fit: cover;">`;
            } else {
                photoElement.innerHTML = '<i class="fas fa-user"></i>';
            }
        }

        // Update contact info
        this.updateContactInfo();

        // Update summary
        this.updateSummaryPreview();

        // Update other sections
        this.updateExperiencePreview();
        this.updateEducationPreview();
        this.updateSkillsPreview();
        this.updateLanguagesPreview();
    }
    
    updateContactInfo() {
        const contactContainer = document.querySelector('.cv-contact');
        if (!contactContainer) return;
        
        const data = this.cvData.personal;
        let contactHTML = '';
        
        if (data.email) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>${data.email}</span>
                </div>
            `;
        }
        
        if (data.phone) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>${data.phone}</span>
                </div>
            `;
        }
        
        if (data.city) {
            contactHTML += `
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>${data.city}</span>
                </div>
            `;
        }
        
        contactContainer.innerHTML = contactHTML;
    }
    
    updateSummaryPreview() {
        const summarySection = document.querySelector('.cv-content .cv-section');
        if (summarySection) {
            const summaryText = summarySection.querySelector('p');
            if (summaryText) {
                summaryText.textContent = this.cvData.summary || 'سيتم عرض الملخص المهني هنا...';
                summaryText.style.opacity = this.cvData.summary ? '1' : '0.5';
            }
        }
    }

    updateExperiencePreview() {
        // This will be implemented when experience data is available
        console.log('Experience preview updated');
    }

    updateEducationPreview() {
        // This will be implemented when education data is available
        console.log('Education preview updated');
    }

    updateSkillsPreview() {
        // This will be implemented when skills data is available
        console.log('Skills preview updated');
    }

    updateLanguagesPreview() {
        // This will be implemented when languages data is available
        console.log('Languages preview updated');
    }

    // Experience Management Functions
    addExperience() {
        const experienceId = Date.now();
        const experienceItem = {
            id: experienceId,
            jobTitle: '',
            company: '',
            startDate: '',
            endDate: '',
            isCurrent: false,
            description: ''
        };

        this.cvData.experience.push(experienceItem);
        this.renderExperienceItem(experienceItem);
        this.updatePreview();
        this.saveToLocalStorage();
    }

    renderExperienceItem(experience) {
        const experienceList = document.getElementById('experienceList');
        if (!experienceList) return;

        const experienceDiv = document.createElement('div');
        experienceDiv.className = 'experience-item';
        experienceDiv.dataset.id = experience.id;

        experienceDiv.innerHTML = `
            <div class="item-header">
                <h4>خبرة عملية ${this.cvData.experience.length}</h4>
                <button class="btn-remove" onclick="removeExperience(${experience.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>المسمى الوظيفي *</label>
                    <input type="text"
                           value="${experience.jobTitle}"
                           onchange="updateExperience(${experience.id}, 'jobTitle', this.value)"
                           placeholder="مثال: مطور ويب أول"
                           required>
                </div>
                <div class="form-group">
                    <label>اسم الشركة *</label>
                    <input type="text"
                           value="${experience.company}"
                           onchange="updateExperience(${experience.id}, 'company', this.value)"
                           placeholder="مثال: شركة التقنية المتقدمة"
                           required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>تاريخ البداية *</label>
                    <input type="month"
                           value="${experience.startDate}"
                           onchange="updateExperience(${experience.id}, 'startDate', this.value)"
                           required>
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="month"
                           value="${experience.endDate}"
                           onchange="updateExperience(${experience.id}, 'endDate', this.value)"
                           ${experience.isCurrent ? 'disabled' : ''}>
                </div>
            </div>

            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox"
                           ${experience.isCurrent ? 'checked' : ''}
                           onchange="updateExperience(${experience.id}, 'isCurrent', this.checked)">
                    <span class="checkmark"></span>
                    أعمل حالياً في هذه الوظيفة
                </label>
            </div>

            <div class="form-group">
                <label>وصف المهام والإنجازات</label>
                <textarea rows="4"
                          onchange="updateExperience(${experience.id}, 'description', this.value)"
                          placeholder="اكتب وصفاً مختصراً لمهامك وإنجازاتك في هذه الوظيفة...">${experience.description}</textarea>
            </div>
        `;

        experienceList.appendChild(experienceDiv);
    }

    removeExperience(experienceId) {
        if (confirm('هل أنت متأكد من حذف هذه الخبرة؟')) {
            this.cvData.experience = this.cvData.experience.filter(exp => exp.id !== experienceId);

            const experienceElement = document.querySelector(`[data-id="${experienceId}"]`);
            if (experienceElement) {
                experienceElement.remove();
            }

            this.updatePreview();
            this.saveToLocalStorage();
            this.showNotification('تم حذف الخبرة بنجاح', 'success');
        }
    }

    updateExperience(experienceId, field, value) {
        const experience = this.cvData.experience.find(exp => exp.id === experienceId);
        if (experience) {
            experience[field] = value;

            // Handle current job checkbox
            if (field === 'isCurrent') {
                const endDateInput = document.querySelector(`[data-id="${experienceId}"] input[type="month"]:last-of-type`);
                if (endDateInput) {
                    endDateInput.disabled = value;
                    if (value) {
                        endDateInput.value = '';
                        experience.endDate = '';
                    }
                }
            }

            this.updatePreview();
            this.saveToLocalStorage();
        }
    }

    // Education Management Functions
    addEducation() {
        const educationId = Date.now();
        const educationItem = {
            id: educationId,
            degree: '',
            institution: '',
            year: '',
            gpa: '',
            details: ''
        };

        this.cvData.education.push(educationItem);
        this.renderEducationItem(educationItem);
        this.updatePreview();
        this.saveToLocalStorage();
    }

    renderEducationItem(education) {
        const educationList = document.getElementById('educationList');
        if (!educationList) return;

        const educationDiv = document.createElement('div');
        educationDiv.className = 'education-item';
        educationDiv.dataset.id = education.id;

        educationDiv.innerHTML = `
            <div class="item-header">
                <h4>مؤهل أكاديمي ${this.cvData.education.length}</h4>
                <button class="btn-remove" onclick="removeEducation(${education.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>الدرجة العلمية *</label>
                    <input type="text"
                           value="${education.degree}"
                           onchange="updateEducation(${education.id}, 'degree', this.value)"
                           placeholder="مثال: بكالوريوس علوم الحاسب"
                           required>
                </div>
                <div class="form-group">
                    <label>اسم الجامعة/المؤسسة *</label>
                    <input type="text"
                           value="${education.institution}"
                           onchange="updateEducation(${education.id}, 'institution', this.value)"
                           placeholder="مثال: جامعة الملك سعود"
                           required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>سنة التخرج *</label>
                    <input type="number"
                           value="${education.year}"
                           onchange="updateEducation(${education.id}, 'year', this.value)"
                           placeholder="2023"
                           min="1950"
                           max="2030"
                           required>
                </div>
                <div class="form-group">
                    <label>المعدل التراكمي (اختياري)</label>
                    <input type="text"
                           value="${education.gpa}"
                           onchange="updateEducation(${education.id}, 'gpa', this.value)"
                           placeholder="مثال: 3.8 من 4.0">
                </div>
            </div>

            <div class="form-group">
                <label>تفاصيل إضافية (اختياري)</label>
                <textarea rows="3"
                          onchange="updateEducation(${education.id}, 'details', this.value)"
                          placeholder="مثال: تخصص فرعي، مشاريع التخرج، الأنشطة الطلابية...">${education.details}</textarea>
            </div>
        `;

        educationList.appendChild(educationDiv);
    }

    removeEducation(educationId) {
        if (confirm('هل أنت متأكد من حذف هذا المؤهل؟')) {
            this.cvData.education = this.cvData.education.filter(edu => edu.id !== educationId);

            const educationElement = document.querySelector(`[data-id="${educationId}"]`);
            if (educationElement) {
                educationElement.remove();
            }

            this.updatePreview();
            this.saveToLocalStorage();
            this.showNotification('تم حذف المؤهل بنجاح', 'success');
        }
    }

    updateEducation(educationId, field, value) {
        const education = this.cvData.education.find(edu => edu.id === educationId);
        if (education) {
            education[field] = value;
            this.updatePreview();
            this.saveToLocalStorage();
        }
    }

    loadSelectedTemplate() {
        // Check URL parameters for template
        const urlParams = new URLSearchParams(window.location.search);
        const templateId = urlParams.get('template');

        if (templateId) {
            this.selectedTemplate = {
                id: parseInt(templateId),
                loadedAt: Date.now()
            };

            // Show template selection notification
            this.showTemplateNotification(templateId);
        }

        // Check localStorage for selected template
        const savedTemplate = localStorage.getItem('selected_template');
        if (savedTemplate && !this.selectedTemplate) {
            try {
                const templateData = JSON.parse(savedTemplate);
                this.selectedTemplate = templateData;
                this.showTemplateNotification(templateData.id, templateData.name);
            } catch (error) {
                console.error('Error loading saved template:', error);
            }
        }
    }

    showTemplateNotification(templateId, templateName) {
        const notification = document.createElement('div');
        notification.className = 'template-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-check-circle"></i>
                <div class="notification-text">
                    <h4>تم تحديد القالب بنجاح!</h4>
                    <p>${templateName || `القالب رقم ${templateId}`} جاهز للاستخدام</p>
                </div>
                <button onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--success-color);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            animation: slideInDown 0.3s ease-out;
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideOutUp 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    applyTemplateStyles() {
        if (!this.selectedTemplate) return;

        const templateId = this.selectedTemplate.id;
        const previewDocument = document.querySelector('.cv-preview-document');

        if (previewDocument) {
            // Remove existing template classes
            previewDocument.className = previewDocument.className
                .replace(/template-\d+/g, '')
                .replace(/\s+/g, ' ')
                .trim();

            // Add new template class
            previewDocument.classList.add(`template-${templateId}`);

            // Apply template-specific styles
            this.applyTemplateSpecificStyles(templateId);
        }
    }

    applyTemplateSpecificStyles(templateId) {
        const styleMap = {
            1: 'modern-template',
            2: 'classic-template',
            3: 'creative-template',
            4: 'minimal-template',
            5: 'professional-template'
        };

        const templateClass = styleMap[templateId] || 'modern-template';
        const previewDocument = document.querySelector('.cv-preview-document');

        if (previewDocument) {
            // Remove existing template style classes
            Object.values(styleMap).forEach(className => {
                previewDocument.classList.remove(className);
            });

            // Add the selected template class
            previewDocument.classList.add(templateClass);

            // Update preview immediately
            this.updatePreview();
        }
    }
    
    markSectionCompleted(sectionName) {
        const sectionItem = document.querySelector(`[data-section="${sectionName}"]`);
        const statusIcon = sectionItem.querySelector('.section-status i');
        
        if (this.isSectionComplete(sectionName)) {
            statusIcon.className = 'fas fa-check-circle';
            statusIcon.style.color = 'var(--accent-color)';
        }
    }
    
    isSectionComplete(sectionName) {
        switch (sectionName) {
            case 'personal':
                return this.cvData.personal.fullName &&
                       this.cvData.personal.jobTitle &&
                       this.cvData.personal.email &&
                       this.cvData.personal.phone;
            case 'summary':
                return this.cvData.summary && this.cvData.summary.length >= 50;
            case 'experience':
                return this.cvData.experience && this.cvData.experience.length > 0;
            case 'education':
                return this.cvData.education && this.cvData.education.length > 0;
            case 'skills':
                return (this.cvData.skills.technical && this.cvData.skills.technical.length > 0) ||
                       (this.cvData.skills.soft && this.cvData.skills.soft.length > 0);
            case 'languages':
                return this.cvData.languages && this.cvData.languages.length > 0;
            default:
                return false;
        }
    }
    
    updateProgress() {
        const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
        const completedSections = sections.filter(section => this.isSectionComplete(section));

        const totalSections = sections.length;
        const progress = (completedSections.length / totalSections) * 100;

        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
            progressFill.style.transition = 'width 0.3s ease';
        }

        if (progressText) {
            progressText.textContent = `${Math.round(progress)}% مكتمل`;

            // Change color based on progress
            if (progress < 30) {
                progressText.style.color = 'var(--danger-color)';
            } else if (progress < 70) {
                progressText.style.color = 'var(--warning-color)';
            } else {
                progressText.style.color = 'var(--success-color)';
            }
        }

        // Update section status indicators
        sections.forEach(section => {
            const sectionItem = document.querySelector(`[data-section="${section}"]`);
            if (sectionItem) {
                const statusIcon = sectionItem.querySelector('.section-status i');
                if (statusIcon) {
                    if (this.isSectionComplete(section)) {
                        statusIcon.className = 'fas fa-check-circle';
                        statusIcon.style.color = 'var(--success-color)';
                    } else {
                        statusIcon.className = 'fas fa-circle';
                        statusIcon.style.color = 'var(--text-light)';
                    }
                }
            }
        });
    }
    
    setupAutoSave() {
        setInterval(() => {
            this.saveToLocalStorage();
        }, 30000); // Auto-save every 30 seconds
    }
    
    saveToLocalStorage() {
        localStorage.setItem('elashrafy_cv_data', JSON.stringify(this.cvData));
        localStorage.setItem('elashrafy_cv_current_section', this.currentSection);
    }
    
    loadSavedData() {
        const savedData = localStorage.getItem('elashrafy_cv_data');
        const savedSection = localStorage.getItem('elashrafy_cv_current_section');
        
        if (savedData) {
            this.cvData = JSON.parse(savedData);
            this.populateForm();
        }
        
        if (savedSection) {
            this.switchSection(savedSection);
        }
    }
    
    populateForm() {
        // Populate personal information
        Object.keys(this.cvData.personal).forEach(key => {
            const input = document.getElementById(key);
            if (input) {
                input.value = this.cvData.personal[key];
            }
        });
        
        // Update preview
        this.updatePreview();
    }
}

// Global functions
function nextSection() {
    const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
    const currentIndex = sections.indexOf(window.cvBuilder.currentSection);
    if (currentIndex < sections.length - 1) {
        window.cvBuilder.switchSection(sections[currentIndex + 1]);
    }
}

function previousSection() {
    const sections = ['personal', 'summary', 'experience', 'education', 'skills', 'languages'];
    const currentIndex = sections.indexOf(window.cvBuilder.currentSection);
    if (currentIndex > 0) {
        window.cvBuilder.switchSection(sections[currentIndex - 1]);
    }
}

function removePhoto() {
    const photoPreview = document.getElementById('photoPreview');
    photoPreview.innerHTML = '<i class="fas fa-user"></i>';
    
    window.cvBuilder.cvData.personal.photo = null;
    window.cvBuilder.updatePreview();
}

function saveCV() {
    window.cvBuilder.saveToLocalStorage();
    // Show success notification
    if (window.app) {
        window.app.showNotification('تم حفظ السيرة الذاتية بنجاح', 'success');
    }
}

function previewCV() {
    // Check if CV has content
    if (!window.cvBuilder || !window.cvBuilder.cvData || !window.cvBuilder.cvData.personal.fullName) {
        showNotification('لا توجد بيانات كافية للمعاينة. يرجى إدخال معلوماتك الشخصية على الأقل.', 'warning');
        return;
    }

    // Create preview modal
    const modal = document.createElement('div');
    modal.className = 'modal preview-modal-overlay';
    modal.innerHTML = `
        <div class="modal-content preview-modal-content">
            <div class="preview-modal-header">
                <div class="preview-header-content">
                    <div class="preview-icon-main">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="preview-title-section">
                        <h3>معاينة السيرة الذاتية</h3>
                        <p>معاينة كاملة لسيرتك الذاتية قبل التصدير</p>
                    </div>
                </div>
                <div class="preview-controls">
                    <button class="preview-control-btn" onclick="zoomPreview('out')" title="تصغير">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <span class="zoom-level" id="previewZoomLevel">100%</span>
                    <button class="preview-control-btn" onclick="zoomPreview('in')" title="تكبير">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="preview-close-btn" onclick="closePreviewModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="preview-modal-body">
                <div class="preview-container" id="previewContainer">
                    <div class="cv-preview-document" id="fullPreviewDocument">
                        <!-- CV content will be generated here -->
                    </div>
                </div>

                <div class="preview-actions">
                    <button class="btn btn-outline" onclick="closePreviewModal()">
                        <i class="fas fa-times"></i>
                        إغلاق
                    </button>
                    <button class="btn btn-primary" onclick="exportCV(); closePreviewModal();">
                        <i class="fas fa-download"></i>
                        تصدير السيرة الذاتية
                    </button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.appendChild(modal);

    // Generate full CV preview
    generateFullCVPreview();

    // Show modal with animation
    requestAnimationFrame(() => {
        modal.classList.add('modal-show');
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closePreviewModal();
        }
    });

    // Close modal with Escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closePreviewModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

function closePreviewModal() {
    const modal = document.querySelector('.preview-modal-overlay');
    if (modal) {
        modal.classList.remove('modal-show');
        modal.classList.add('modal-hide');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

let previewZoom = 100;

function zoomPreview(direction) {
    const previewDoc = document.getElementById('fullPreviewDocument');
    const zoomLevel = document.getElementById('previewZoomLevel');

    if (!previewDoc || !zoomLevel) return;

    if (direction === 'in' && previewZoom < 150) {
        previewZoom += 10;
    } else if (direction === 'out' && previewZoom > 50) {
        previewZoom -= 10;
    }

    previewDoc.style.transform = `scale(${previewZoom / 100})`;
    zoomLevel.textContent = `${previewZoom}%`;
}

function generateFullCVPreview() {
    const previewContainer = document.getElementById('fullPreviewDocument');
    if (!previewContainer || !window.cvBuilder) return;

    const data = window.cvBuilder.cvData;

    // Generate complete CV HTML
    const cvHTML = `
        <div class="cv-page full-preview">
            <div class="cv-header">
                <div class="cv-photo">
                    ${data.personal.photo ?
                        `<img src="${data.personal.photo}" alt="Profile Photo">` :
                        '<i class="fas fa-user"></i>'
                    }
                </div>
                <div class="cv-basic-info">
                    <h1 class="cv-name">${data.personal.fullName || 'اسمك هنا'}</h1>
                    <h2 class="cv-title">${data.personal.jobTitle || 'المسمى الوظيفي'}</h2>
                    <div class="cv-contact">
                        ${data.personal.email ? `
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <span>${data.personal.email}</span>
                            </div>
                        ` : ''}
                        ${data.personal.phone ? `
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <span>${data.personal.phone}</span>
                            </div>
                        ` : ''}
                        ${data.personal.city ? `
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>${data.personal.city}</span>
                            </div>
                        ` : ''}
                        ${data.personal.website ? `
                            <div class="contact-item">
                                <i class="fas fa-globe"></i>
                                <span>${data.personal.website}</span>
                            </div>
                        ` : ''}
                        ${data.personal.linkedin ? `
                            <div class="contact-item">
                                <i class="fab fa-linkedin"></i>
                                <span>${data.personal.linkedin}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>

            <div class="cv-content">
                ${data.summary ? `
                    <div class="cv-section">
                        <h3 class="section-title">الملخص المهني</h3>
                        <p class="summary-text">${data.summary}</p>
                    </div>
                ` : ''}

                ${data.experience && data.experience.length > 0 ? `
                    <div class="cv-section">
                        <h3 class="section-title">الخبرات العملية</h3>
                        <div class="experience-list">
                            ${data.experience.map(exp => `
                                <div class="cv-experience-item">
                                    <h4>${exp.jobTitle || 'المسمى الوظيفي'}</h4>
                                    <div class="company">${exp.company || 'اسم الشركة'}</div>
                                    <div class="duration">
                                        ${exp.startDate || 'تاريخ البداية'} -
                                        ${exp.isCurrent ? 'حتى الآن' : (exp.endDate || 'تاريخ النهاية')}
                                    </div>
                                    ${exp.description ? `<div class="description">${exp.description}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                ${data.education && data.education.length > 0 ? `
                    <div class="cv-section">
                        <h3 class="section-title">التعليم</h3>
                        <div class="education-list">
                            ${data.education.map(edu => `
                                <div class="cv-education-item">
                                    <h4>${edu.degree || 'الدرجة العلمية'}</h4>
                                    <div class="institution">${edu.institution || 'اسم الجامعة'}</div>
                                    <div class="duration">${edu.year || 'سنة التخرج'}</div>
                                    ${edu.gpa ? `<div class="gpa">المعدل: ${edu.gpa}</div>` : ''}
                                    ${edu.details ? `<div class="description">${edu.details}</div>` : ''}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                ${(data.skills && (data.skills.technical?.length > 0 || data.skills.soft?.length > 0)) ? `
                    <div class="cv-section">
                        <h3 class="section-title">المهارات</h3>
                        <div class="cv-skills-grid">
                            ${data.skills.technical ? data.skills.technical.map(skill =>
                                `<div class="cv-skill-item">${skill}</div>`
                            ).join('') : ''}
                            ${data.skills.soft ? data.skills.soft.map(skill =>
                                `<div class="cv-skill-item soft-skill">${skill}</div>`
                            ).join('') : ''}
                        </div>
                    </div>
                ` : ''}

                ${data.languages && data.languages.length > 0 ? `
                    <div class="cv-section">
                        <h3 class="section-title">اللغات</h3>
                        <div class="languages-list">
                            ${data.languages.map(lang => `
                                <div class="cv-language-item">
                                    <span class="language-name">${lang.name || 'اسم اللغة'}</span>
                                    <span class="language-level">${lang.level || 'المستوى'}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        </div>
    `;

    previewContainer.innerHTML = cvHTML;
}

function exportCV() {
    // Check if CV has content
    if (!window.cvBuilder || !window.cvBuilder.cvData || !window.cvBuilder.cvData.personal.fullName) {
        showNotification('لا توجد بيانات كافية للتصدير. يرجى إدخال معلوماتك الشخصية على الأقل.', 'warning');
        return;
    }

    // Show improved export options modal
    const modal = document.createElement('div');
    modal.className = 'modal export-modal-overlay';
    modal.innerHTML = `
        <div class="modal-content export-modal-content">
            <div class="export-modal-header">
                <div class="export-header-content">
                    <div class="export-icon-main">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="export-title-section">
                        <h3>تصدير السيرة الذاتية</h3>
                        <p>اختر تنسيق التصدير المناسب لاحتياجاتك</p>
                    </div>
                </div>
                <button class="export-close-btn" onclick="closeExportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="export-modal-body">
                <div class="export-options-grid">
                    <div class="export-option-card pdf-card" onclick="exportToPDF()">
                        <div class="option-icon-wrapper pdf-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="option-content">
                            <h4>ملف PDF</h4>
                            <p>الأفضل للطباعة والمشاركة المهنية</p>
                            <div class="option-features">
                                <span class="feature-tag">جودة عالية</span>
                                <span class="feature-tag">قابل للطباعة</span>
                                <span class="feature-tag">احترافي</span>
                            </div>
                        </div>
                        <div class="option-action">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                    </div>

                    <div class="export-option-card png-card" onclick="exportToImage('png')">
                        <div class="option-icon-wrapper png-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="option-content">
                            <h4>صورة PNG</h4>
                            <p>صورة عالية الجودة مع خلفية شفافة</p>
                            <div class="option-features">
                                <span class="feature-tag">دقة عالية</span>
                                <span class="feature-tag">خلفية شفافة</span>
                                <span class="feature-tag">للويب</span>
                            </div>
                        </div>
                        <div class="option-action">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                    </div>

                    <div class="export-option-card jpg-card" onclick="exportToImage('jpg')">
                        <div class="option-icon-wrapper jpg-icon">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="option-content">
                            <h4>صورة JPG</h4>
                            <p>صورة مضغوطة مناسبة للمشاركة السريعة</p>
                            <div class="option-features">
                                <span class="feature-tag">حجم صغير</span>
                                <span class="feature-tag">سريع التحميل</span>
                                <span class="feature-tag">للمشاركة</span>
                            </div>
                        </div>
                        <div class="option-action">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                    </div>

                    <div class="export-option-card qr-card" onclick="generateQRCode()">
                        <div class="option-icon-wrapper qr-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="option-content">
                            <h4>رمز QR</h4>
                            <p>رمز سريع لمعلومات الاتصال الخاصة بك</p>
                            <div class="option-features">
                                <span class="feature-tag">سهل المشاركة</span>
                                <span class="feature-tag">معلومات الاتصال</span>
                                <span class="feature-tag">تفاعلي</span>
                            </div>
                        </div>
                        <div class="option-action">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                    </div>
                </div>

                <div class="export-tips-section">
                    <div class="tip-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <div class="tip-content">
                        <strong>نصيحة مهمة:</strong> ننصح بتصدير ملف PDF للاستخدام المهني والتقديم للوظائف، وصورة PNG للمشاركة على وسائل التواصل الاجتماعي.
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page with smooth animation
    document.body.appendChild(modal);

    // Trigger entrance animation
    requestAnimationFrame(() => {
        modal.classList.add('modal-show');
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeExportModal();
        }
    });

    // Close modal with Escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            closeExportModal();
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

function closeExportModal() {
    const modal = document.querySelector('.export-modal-overlay');
    if (modal) {
        modal.classList.remove('modal-show');
        modal.classList.add('modal-hide');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Additional CV Builder Functions

function addExperience() {
    const experienceList = document.getElementById('experienceList');
    if (!experienceList) return;

    const experienceId = Date.now();

    const experienceItem = document.createElement('div');
    experienceItem.className = 'experience-item';
    experienceItem.dataset.id = experienceId;

    experienceItem.innerHTML = `
        <div class="item-header">
            <h4>خبرة عملية جديدة</h4>
            <button class="btn-remove" onclick="removeExperience(${experienceId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>المسمى الوظيفي *</label>
                <input type="text" name="jobTitle" placeholder="مثل: مطور ويب أول" required
                       onchange="updateExperienceData(${experienceId})">
            </div>
            <div class="form-group">
                <label>اسم الشركة *</label>
                <input type="text" name="company" placeholder="مثل: شركة التقنية المتقدمة" required
                       onchange="updateExperienceData(${experienceId})">
            </div>
            <div class="form-group">
                <label>تاريخ البداية *</label>
                <input type="month" name="startDate" required onchange="updateExperienceData(${experienceId})">
            </div>
            <div class="form-group">
                <label>تاريخ النهاية</label>
                <input type="month" name="endDate" onchange="updateExperienceData(${experienceId})">
                <label class="checkbox-label">
                    <input type="checkbox" name="isCurrent" onchange="toggleCurrentJob(this, ${experienceId})">
                    <span>أعمل حالياً في هذه الوظيفة</span>
                </label>
            </div>
            <div class="form-group full-width">
                <label>وصف المهام والإنجازات</label>
                <textarea rows="4" name="description" placeholder="اكتب وصفاً مفصلاً عن مهامك وإنجازاتك في هذه الوظيفة..."
                          onchange="updateExperienceData(${experienceId})"></textarea>
            </div>
        </div>
    `;

    experienceList.appendChild(experienceItem);

    // Initialize experience data
    if (!window.cvBuilder.cvData.experience) {
        window.cvBuilder.cvData.experience = [];
    }

    window.cvBuilder.cvData.experience.push({
        id: experienceId,
        jobTitle: '',
        company: '',
        startDate: '',
        endDate: '',
        isCurrent: false,
        description: ''
    });

    // Add animation
    experienceItem.style.opacity = '0';
    experienceItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        experienceItem.style.transition = 'all 0.3s ease';
        experienceItem.style.opacity = '1';
        experienceItem.style.transform = 'translateY(0)';
    }, 100);

    // Update progress
    window.cvBuilder.updateProgress();
}

function updateExperienceData(experienceId) {
    const experienceItem = document.querySelector(`[data-id="${experienceId}"]`);
    if (!experienceItem || !window.cvBuilder) return;

    const experience = window.cvBuilder.cvData.experience.find(exp => exp.id === experienceId);
    if (!experience) return;

    // Update experience data from form inputs
    const inputs = experienceItem.querySelectorAll('input, textarea');
    inputs.forEach(input => {
        if (input.type === 'checkbox') {
            experience[input.name] = input.checked;
        } else {
            experience[input.name] = input.value;
        }
    });

    // Update preview and progress
    window.cvBuilder.updatePreview();
    window.cvBuilder.updateProgress();
    window.cvBuilder.saveToLocalStorage();
}

function removeExperience(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        // Remove from data
        if (window.cvBuilder && window.cvBuilder.cvData.experience) {
            window.cvBuilder.cvData.experience = window.cvBuilder.cvData.experience.filter(exp => exp.id !== id);
            window.cvBuilder.updateProgress();
            window.cvBuilder.saveToLocalStorage();
        }

        // Remove from DOM with animation
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function addEducation() {
    const educationList = document.getElementById('educationList');
    const educationId = Date.now();

    const educationItem = document.createElement('div');
    educationItem.className = 'education-item';
    educationItem.dataset.id = educationId;

    educationItem.innerHTML = `
        <div class="item-header">
            <h4>مؤهل أكاديمي جديد</h4>
            <button class="btn-remove" onclick="removeEducation(${educationId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>الدرجة العلمية *</label>
                <input type="text" placeholder="مثل: بكالوريوس علوم الحاسب" required>
            </div>
            <div class="form-group">
                <label>اسم الجامعة/المؤسسة *</label>
                <input type="text" placeholder="مثل: جامعة الملك سعود" required>
            </div>
            <div class="form-group">
                <label>سنة التخرج *</label>
                <input type="number" min="1950" max="2030" placeholder="2020" required>
            </div>
            <div class="form-group">
                <label>المعدل التراكمي</label>
                <input type="text" placeholder="3.8 من 4.0">
            </div>
            <div class="form-group full-width">
                <label>تفاصيل إضافية</label>
                <textarea rows="3" placeholder="اذكر أي تفاصيل إضافية مثل التخصص الفرعي، المشاريع المميزة، إلخ..."></textarea>
            </div>
        </div>
    `;

    educationList.appendChild(educationItem);

    // Add animation
    educationItem.style.opacity = '0';
    educationItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        educationItem.style.transition = 'all 0.3s ease';
        educationItem.style.opacity = '1';
        educationItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeEducation(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function addLanguage() {
    const languagesList = document.getElementById('languagesList');
    const languageId = Date.now();

    const languageItem = document.createElement('div');
    languageItem.className = 'language-item';
    languageItem.dataset.id = languageId;

    languageItem.innerHTML = `
        <div class="item-header">
            <h4>لغة جديدة</h4>
            <button class="btn-remove" onclick="removeLanguage(${languageId})">
                <i class="fas fa-trash"></i>
            </button>
        </div>
        <div class="form-grid">
            <div class="form-group">
                <label>اسم اللغة *</label>
                <input type="text" placeholder="مثل: الإنجليزية" required>
            </div>
            <div class="form-group">
                <label>مستوى الإتقان *</label>
                <select required>
                    <option value="">اختر المستوى</option>
                    <option value="مبتدئ">مبتدئ</option>
                    <option value="متوسط">متوسط</option>
                    <option value="متقدم">متقدم</option>
                    <option value="ممتاز">ممتاز</option>
                    <option value="لغة أم">لغة أم</option>
                </select>
            </div>
        </div>
    `;

    languagesList.appendChild(languageItem);

    // Add animation
    languageItem.style.opacity = '0';
    languageItem.style.transform = 'translateY(20px)';
    setTimeout(() => {
        languageItem.style.transition = 'all 0.3s ease';
        languageItem.style.opacity = '1';
        languageItem.style.transform = 'translateY(0)';
    }, 100);
}

function removeLanguage(id) {
    const item = document.querySelector(`[data-id="${id}"]`);
    if (item) {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '0';
        item.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            item.remove();
        }, 300);
    }
}

function toggleCurrentJob(checkbox, experienceId) {
    const endDateInput = checkbox.closest('.form-group').querySelector('input[type="month"]');
    if (checkbox.checked) {
        endDateInput.disabled = true;
        endDateInput.value = '';
        endDateInput.placeholder = 'حتى الآن';
        endDateInput.style.backgroundColor = 'var(--bg-secondary)';
    } else {
        endDateInput.disabled = false;
        endDateInput.placeholder = '';
        endDateInput.style.backgroundColor = '';
    }

    // Update experience data
    if (experienceId) {
        updateExperienceData(experienceId);
    }
}

function addSuggestedSkill(skill, type) {
    const inputId = type === 'technical' ? 'technicalSkillInput' : 'softSkillInput';
    const listId = type === 'technical' ? 'technicalSkillsList' : 'softSkillsList';

    addSkillToList(skill, listId);
}

function addSkillToList(skill, listId) {
    const skillsList = document.getElementById(listId);
    if (!skillsList || !skill.trim()) return;

    // Check if skill already exists
    const existingSkills = Array.from(skillsList.querySelectorAll('.skill-tag')).map(tag =>
        tag.querySelector('span').textContent.trim()
    );
    if (existingSkills.includes(skill.trim())) {
        // Show notification that skill already exists
        if (window.app) {
            window.app.showNotification('هذه المهارة موجودة بالفعل', 'warning');
        }
        return;
    }

    const skillTag = document.createElement('div');
    skillTag.className = 'skill-tag';
    skillTag.innerHTML = `
        <span>${skill.trim()}</span>
        <button onclick="removeSkill(this, '${listId}')" class="skill-remove" title="حذف المهارة">
            <i class="fas fa-times"></i>
        </button>
    `;

    skillsList.appendChild(skillTag);

    // Update CV data
    if (window.cvBuilder) {
        const skillType = listId.includes('technical') ? 'technical' : 'soft';
        if (!window.cvBuilder.cvData.skills[skillType]) {
            window.cvBuilder.cvData.skills[skillType] = [];
        }
        window.cvBuilder.cvData.skills[skillType].push(skill.trim());

        // Update progress and save
        window.cvBuilder.updateProgress();
        window.cvBuilder.updatePreview();
        window.cvBuilder.saveToLocalStorage();
    }

    // Add animation
    skillTag.style.opacity = '0';
    skillTag.style.transform = 'scale(0.8)';
    setTimeout(() => {
        skillTag.style.transition = 'all 0.3s ease';
        skillTag.style.opacity = '1';
        skillTag.style.transform = 'scale(1)';
    }, 100);
}

function removeSkill(button, listId) {
    const skillTag = button.closest('.skill-tag');
    const skillText = skillTag.querySelector('span').textContent.trim();

    // Remove from CV data
    if (window.cvBuilder && listId) {
        const skillType = listId.includes('technical') ? 'technical' : 'soft';
        if (window.cvBuilder.cvData.skills[skillType]) {
            window.cvBuilder.cvData.skills[skillType] = window.cvBuilder.cvData.skills[skillType]
                .filter(skill => skill !== skillText);

            // Update progress and save
            window.cvBuilder.updateProgress();
            window.cvBuilder.updatePreview();
            window.cvBuilder.saveToLocalStorage();
        }
    }

    // Remove from DOM with animation
    skillTag.style.transition = 'all 0.3s ease';
    skillTag.style.opacity = '0';
    skillTag.style.transform = 'scale(0.8)';
    setTimeout(() => {
        skillTag.remove();
    }, 300);
}

function setupSkillsInput() {
    const technicalInput = document.getElementById('technicalSkillInput');
    const softInput = document.getElementById('softSkillInput');

    if (technicalInput) {
        // Handle Enter key
        technicalInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const skill = e.target.value.trim();
                if (skill) {
                    addSkillToList(skill, 'technicalSkillsList');
                    e.target.value = '';
                }
            }
        });

        // Handle blur event (when user clicks away)
        technicalInput.addEventListener('blur', (e) => {
            const skill = e.target.value.trim();
            if (skill) {
                addSkillToList(skill, 'technicalSkillsList');
                e.target.value = '';
            }
        });

        // Add placeholder suggestions
        technicalInput.addEventListener('focus', () => {
            technicalInput.placeholder = 'مثل: JavaScript, React, Python - اضغط Enter للإضافة';
        });

        technicalInput.addEventListener('blur', () => {
            technicalInput.placeholder = 'أدخل مهارة تقنية واضغط Enter';
        });
    }

    if (softInput) {
        // Handle Enter key
        softInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                const skill = e.target.value.trim();
                if (skill) {
                    addSkillToList(skill, 'softSkillsList');
                    e.target.value = '';
                }
            }
        });

        // Handle blur event
        softInput.addEventListener('blur', (e) => {
            const skill = e.target.value.trim();
            if (skill) {
                addSkillToList(skill, 'softSkillsList');
                e.target.value = '';
            }
        });

        // Add placeholder suggestions
        softInput.addEventListener('focus', () => {
            softInput.placeholder = 'مثل: العمل الجماعي, القيادة, التواصل - اضغط Enter للإضافة';
        });

        softInput.addEventListener('blur', () => {
            softInput.placeholder = 'أدخل مهارة شخصية واضغط Enter';
        });
    }
}

function applySuggestion(suggestionId) {
    const suggestions = {
        'summary1': 'أضف المزيد من التفاصيل حول خبراتك التقنية والأدوات التي تستخدمها',
        'summary2': 'اذكر إنجازاتك الرئيسية بأرقام محددة (مثل: زيادة الإنتاجية بنسبة 30%)',
        'summary3': 'أضف أهدافك المهنية وما تسعى لتحقيقه في المستقبل'
    };

    const suggestion = suggestions[suggestionId];
    if (suggestion && window.aiAssistant) {
        window.aiAssistant.showSuggestion(suggestion, 'tip');
    }
}

// Enhanced Summary Functions
function updateSummaryStats(textarea) {
    const text = textarea.value;
    const charCount = text.length;
    const wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;

    // Update character count
    const charCountElement = document.getElementById('summaryCount');
    if (charCountElement) {
        charCountElement.textContent = charCount;

        // Update color based on length
        if (charCount < 50) {
            charCountElement.style.color = 'var(--danger-color)';
        } else if (charCount > 500) {
            charCountElement.style.color = 'var(--warning-color)';
        } else {
            charCountElement.style.color = 'var(--success-color)';
        }
    }

    // Update word count
    const wordCountElement = document.getElementById('wordCount');
    if (wordCountElement) {
        wordCountElement.textContent = wordCount;
    }

    // Update quality indicator
    const qualityElement = document.getElementById('qualityIndicator');
    if (qualityElement) {
        let quality = 'ضعيف';
        let className = 'quality-poor';

        if (wordCount >= 80 && wordCount <= 150 && charCount >= 200) {
            quality = 'ممتاز';
            className = 'quality-excellent';
        } else if (wordCount >= 50 && wordCount <= 200 && charCount >= 150) {
            quality = 'جيد';
            className = 'quality-good';
        } else if (wordCount >= 30 && charCount >= 100) {
            quality = 'مقبول';
            className = 'quality-fair';
        }

        qualityElement.textContent = quality;
        qualityElement.className = className;
    }
}

function showSummaryHelp() {
    const helpElement = document.getElementById('summaryHelp');
    if (helpElement) {
        helpElement.style.display = 'block';
        helpElement.style.animation = 'slideInDown 0.3s ease-out';
    }
}

function hideSummaryHelp() {
    const helpElement = document.getElementById('summaryHelp');
    if (helpElement) {
        setTimeout(() => {
            helpElement.style.animation = 'slideOutUp 0.3s ease-in';
            setTimeout(() => {
                helpElement.style.display = 'none';
            }, 300);
        }, 2000); // Hide after 2 seconds
    }
}

function applySummaryTemplate(templateType) {
    const templates = {
        'technical': `مطور [مجالك] متخصص بخبرة [عدد السنوات] سنوات في [التقنيات الرئيسية]. نجحت في تطوير [عدد المشاريع]+ مشروع وتحسين الأداء بنسبة [نسبة التحسين]%. أسعى لتطوير حلول تقنية مبتكرة تساهم في نمو الشركة.`,

        'management': `مدير [نوع الإدارة] بخبرة [عدد السنوات] سنوات في قيادة فرق العمل وإدارة المشاريع. قدت [عدد الفرق] فريق عمل ونجحت في تحقيق [الإنجازات الرئيسية]. أسعى لتطوير استراتيجيات إدارية فعالة تحقق أهداف المؤسسة.`,

        'creative': `مصمم [نوع التصميم] إبداعي بخبرة [عدد السنوات] سنوات في [البرامج والأدوات]. صممت [عدد المشاريع]+ مشروع إبداعي لعملاء متنوعين وحققت [الإنجازات]. أسعى لتقديم حلول بصرية مبتكرة تترك أثراً إيجابياً.`,

        'sales': `أخصائي مبيعات بخبرة [عدد السنوات] سنوات في [نوع المبيعات]. حققت [نسبة تجاوز الأهداف]% من أهداف المبيعات وزدت قاعدة العملاء بنسبة [نسبة الزيادة]%. أسعى لبناء علاقات قوية مع العملاء وتحقيق نمو مستدام في المبيعات.`
    };

    const template = templates[templateType];
    if (template) {
        const summaryTextarea = document.getElementById('professionalSummary');
        if (summaryTextarea) {
            summaryTextarea.value = template;
            summaryTextarea.focus();

            // Trigger input event to update stats
            summaryTextarea.dispatchEvent(new Event('input'));

            // Update CV data
            if (window.cvBuilder) {
                window.cvBuilder.cvData.summary = template;
                window.cvBuilder.updatePreview();
            }

            showNotification(`تم تطبيق القالب ${getTemplateTypeName(templateType)} بنجاح`, 'success');
        }
    }
}

function getTemplateTypeName(type) {
    const names = {
        'technical': 'التقني',
        'management': 'الإداري',
        'creative': 'الإبداعي',
        'sales': 'المبيعات'
    };
    return names[type] || type;
}

function improveSummary() {
    const summaryTextarea = document.getElementById('professionalSummary');
    if (!summaryTextarea || !summaryTextarea.value.trim()) {
        showNotification('يرجى كتابة ملخص أولاً ليتم تحسينه', 'warning');
        return;
    }

    const currentSummary = summaryTextarea.value.trim();

    // Simple improvement suggestions
    let improvedSummary = currentSummary;

    // Add numbers if missing
    if (!/\d+/.test(improvedSummary)) {
        improvedSummary = improvedSummary.replace(/خبرة/g, 'خبرة 5');
        improvedSummary = improvedSummary.replace(/سنوات/g, 'سنوات');
    }

    // Add percentage if missing
    if (!/\d+%/.test(improvedSummary)) {
        improvedSummary += ' وحققت تحسناً في الأداء بنسبة 25%.';
    }

    // Add future goals if missing
    if (!improvedSummary.includes('أسعى') && !improvedSummary.includes('أهدف')) {
        improvedSummary += ' أسعى لتطوير مهاراتي وتحقيق المزيد من النجاحات المهنية.';
    }

    summaryTextarea.value = improvedSummary;
    summaryTextarea.dispatchEvent(new Event('input'));

    if (window.cvBuilder) {
        window.cvBuilder.cvData.summary = improvedSummary;
        window.cvBuilder.updatePreview();
    }

    showNotification('تم تحسين الملخص المهني بنجاح', 'success');
}

function analyzeSummary() {
    const summaryTextarea = document.getElementById('professionalSummary');
    if (!summaryTextarea || !summaryTextarea.value.trim()) {
        showNotification('يرجى كتابة ملخص أولاً ليتم تحليله', 'warning');
        return;
    }

    const text = summaryTextarea.value.trim();
    const wordCount = text.split(/\s+/).length;
    const charCount = text.length;

    // Analysis criteria
    const hasNumbers = /\d+/.test(text);
    const hasPercentage = /\d+%/.test(text);
    const hasGoals = text.includes('أسعى') || text.includes('أهدف') || text.includes('أطمح');
    const hasSkills = text.includes('خبرة') || text.includes('متخصص') || text.includes('مهارة');
    const hasAchievements = text.includes('نجح') || text.includes('حقق') || text.includes('طور');

    let score = 0;
    let feedback = [];

    // Word count check
    if (wordCount >= 80 && wordCount <= 150) {
        score += 20;
        feedback.push('✅ طول الملخص مناسب');
    } else if (wordCount < 50) {
        feedback.push('⚠️ الملخص قصير جداً، أضف المزيد من التفاصيل');
    } else if (wordCount > 200) {
        feedback.push('⚠️ الملخص طويل جداً، اختصر المحتوى');
    } else {
        score += 10;
        feedback.push('✅ طول الملخص مقبول');
    }

    // Content checks
    if (hasNumbers) {
        score += 15;
        feedback.push('✅ يحتوي على أرقام محددة');
    } else {
        feedback.push('❌ أضف أرقام محددة (سنوات الخبرة، عدد المشاريع)');
    }

    if (hasPercentage) {
        score += 15;
        feedback.push('✅ يحتوي على نسب مئوية للإنجازات');
    } else {
        feedback.push('❌ أضف نسب مئوية للإنجازات');
    }

    if (hasGoals) {
        score += 15;
        feedback.push('✅ يحتوي على أهداف مهنية');
    } else {
        feedback.push('❌ أضف أهدافك المهنية');
    }

    if (hasSkills) {
        score += 15;
        feedback.push('✅ يذكر المهارات والخبرات');
    } else {
        feedback.push('❌ أضف مهاراتك الرئيسية');
    }

    if (hasAchievements) {
        score += 20;
        feedback.push('✅ يحتوي على إنجازات');
    } else {
        feedback.push('❌ أضف إنجازاتك المهنية');
    }

    // Show analysis modal
    const modal = document.createElement('div');
    modal.className = 'modal analysis-modal-overlay';
    modal.innerHTML = `
        <div class="modal-content analysis-modal-content">
            <div class="analysis-header">
                <h3><i class="fas fa-chart-line"></i> تحليل الملخص المهني</h3>
                <button onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="analysis-body">
                <div class="score-section">
                    <div class="score-circle">
                        <div class="score-number">${score}</div>
                        <div class="score-label">من 100</div>
                    </div>
                    <div class="score-description">
                        ${score >= 80 ? 'ممتاز! ملخص مهني قوي' :
                          score >= 60 ? 'جيد، يحتاج بعض التحسينات' :
                          'يحتاج تحسينات كبيرة'}
                    </div>
                </div>
                <div class="feedback-section">
                    <h4>التقييم التفصيلي:</h4>
                    <ul>
                        ${feedback.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>
                <div class="stats-section">
                    <div class="stat-item">
                        <span class="stat-label">عدد الكلمات:</span>
                        <span class="stat-value">${wordCount}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">عدد الأحرف:</span>
                        <span class="stat-value">${charCount}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Show modal with animation
    requestAnimationFrame(() => {
        modal.classList.add('modal-show');
    });

    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

function useSummaryExample(exampleId) {
    const examples = {
        1: 'مطور ويب متخصص بخبرة 4 سنوات في React و Node.js. طورت 15+ تطبيق ويب وحسنت الأداء بنسبة 35%. أسعى لتطوير حلول مبتكرة تحسن تجربة المستخدم.',
        2: 'مدير مشاريع معتمد PMP بخبرة 7 سنوات. قدت 25+ مشروع بميزانية تزيد عن 5 مليون ريال. خبرة في Agile و Scrum وإدارة الفرق متعددة الثقافات.',
        3: 'مصمم جرافيك إبداعي بخبرة 5 سنوات في Photoshop و Illustrator. صممت 100+ هوية بصرية وحملة إعلانية ناجحة. أسعى لتقديم تصاميم مبتكرة تترك أثراً إيجابياً.'
    };

    const example = examples[exampleId];
    if (example) {
        const summaryTextarea = document.getElementById('professionalSummary');
        if (summaryTextarea) {
            summaryTextarea.value = example;
            summaryTextarea.focus();
            summaryTextarea.dispatchEvent(new Event('input'));

            if (window.cvBuilder) {
                window.cvBuilder.cvData.summary = example;
                window.cvBuilder.updatePreview();
            }

            showNotification('تم تطبيق المثال بنجاح', 'success');
        }
    }
}

function finishCV() {
    // Save CV data
    if (window.cvBuilder) {
        window.cvBuilder.saveToLocalStorage();
    }

    // Show completion message
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-check-circle"></i> تم إنشاء سيرتك الذاتية بنجاح!</h3>
            </div>
            <div class="modal-body">
                <p>تهانينا! لقد أكملت إنشاء سيرتك الذاتية. يمكنك الآن معاينتها وتصديرها.</p>
                <div class="completion-actions">
                    <button class="btn btn-primary" onclick="previewCV()">
                        <i class="fas fa-eye"></i>
                        معاينة السيرة الذاتية
                    </button>
                    <button class="btn btn-outline" onclick="exportCV()">
                        <i class="fas fa-download"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function zoomIn() {
    const preview = document.querySelector('.cv-preview-document');
    const zoomLevel = document.querySelector('.zoom-level');

    if (preview && zoomLevel) {
        let currentZoom = parseInt(zoomLevel.textContent) || 100;
        currentZoom = Math.min(currentZoom + 10, 150);

        preview.style.transform = `scale(${currentZoom / 100})`;
        zoomLevel.textContent = `${currentZoom}%`;
    }
}

function zoomOut() {
    const preview = document.querySelector('.cv-preview-document');
    const zoomLevel = document.querySelector('.zoom-level');

    if (preview && zoomLevel) {
        let currentZoom = parseInt(zoomLevel.textContent) || 100;
        currentZoom = Math.max(currentZoom - 10, 50);

        preview.style.transform = `scale(${currentZoom / 100})`;
        zoomLevel.textContent = `${currentZoom}%`;
    }
}

// Export functions
function exportToPDF() {
    // Check if libraries are loaded
    if (typeof jsPDF === 'undefined' || typeof html2canvas === 'undefined') {
        showNotification('مكتبات التصدير لم يتم تحميلها بعد. يرجى المحاولة مرة أخرى.', 'warning');
        return;
    }

    // Check if CV has content
    if (!window.cvBuilder || !window.cvBuilder.cvData || !window.cvBuilder.cvData.personal.fullName) {
        showNotification('لا توجد بيانات كافية للتصدير. يرجى إدخال معلوماتك الشخصية على الأقل.', 'warning');
        return;
    }

    // Close export modal
    closeExportModal();

    // Show progress indicator
    if (window.app) {
        const progressIndicator = window.app.showProgressIndicator('جاري تحضير ملف PDF...', 0);

        // Create temporary CV element for export
        const tempContainer = document.createElement('div');
        tempContainer.style.cssText = `
            position: absolute;
            top: -9999px;
            left: -9999px;
            width: 794px;
            background: white;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        `;

        // Generate CV content
        const data = window.cvBuilder.cvData;
        tempContainer.innerHTML = `
            <div class="cv-page pdf-export" style="
                padding: 40px;
                background: white;
                color: #333;
                line-height: 1.6;
                font-size: 14px;
            ">
                <div class="cv-header" style="
                    display: flex;
                    align-items: center;
                    gap: 24px;
                    margin-bottom: 32px;
                    padding-bottom: 20px;
                    border-bottom: 3px solid #1e40af;
                ">
                    ${data.personal.photo ? `
                        <div class="cv-photo" style="
                            width: 120px;
                            height: 120px;
                            border-radius: 50%;
                            overflow: hidden;
                            border: 3px solid #1e40af;
                            flex-shrink: 0;
                        ">
                            <img src="${data.personal.photo}" alt="Profile Photo" style="
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            ">
                        </div>
                    ` : ''}
                    <div class="cv-basic-info" style="flex: 1;">
                        <h1 style="
                            font-size: 2.2rem;
                            font-weight: 700;
                            color: #1e40af;
                            margin: 0 0 8px 0;
                        ">${data.personal.fullName}</h1>
                        <h2 style="
                            font-size: 1.4rem;
                            color: #666;
                            margin: 0 0 16px 0;
                            font-weight: 500;
                        ">${data.personal.jobTitle || ''}</h2>
                        <div class="cv-contact" style="
                            display: flex;
                            flex-wrap: wrap;
                            gap: 16px;
                            font-size: 0.9rem;
                            color: #555;
                        ">
                            ${data.personal.email ? `
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span style="color: #1e40af;">📧</span>
                                    <span>${data.personal.email}</span>
                                </div>
                            ` : ''}
                            ${data.personal.phone ? `
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span style="color: #1e40af;">📱</span>
                                    <span>${data.personal.phone}</span>
                                </div>
                            ` : ''}
                            ${data.personal.city ? `
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span style="color: #1e40af;">📍</span>
                                    <span>${data.personal.city}</span>
                                </div>
                            ` : ''}
                            ${data.personal.website ? `
                                <div style="display: flex; align-items: center; gap: 6px;">
                                    <span style="color: #1e40af;">🌐</span>
                                    <span>${data.personal.website}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <div class="cv-content">
                    ${data.summary ? `
                        <div class="cv-section" style="margin-bottom: 28px;">
                            <h3 style="
                                font-size: 1.3rem;
                                color: #1e40af;
                                margin-bottom: 12px;
                                padding-bottom: 6px;
                                border-bottom: 2px solid #e5e7eb;
                                font-weight: 600;
                            ">الملخص المهني</h3>
                            <p style="
                                line-height: 1.7;
                                color: #555;
                                margin: 0;
                                text-align: justify;
                            ">${data.summary}</p>
                        </div>
                    ` : ''}

                    ${data.experience && data.experience.length > 0 ? `
                        <div class="cv-section" style="margin-bottom: 28px;">
                            <h3 style="
                                font-size: 1.3rem;
                                color: #1e40af;
                                margin-bottom: 12px;
                                padding-bottom: 6px;
                                border-bottom: 2px solid #e5e7eb;
                                font-weight: 600;
                            ">الخبرات العملية</h3>
                            ${data.experience.map(exp => `
                                <div style="
                                    margin-bottom: 20px;
                                    padding: 16px;
                                    background: #f8fafc;
                                    border-radius: 8px;
                                    border-right: 4px solid #1e40af;
                                ">
                                    <h4 style="
                                        font-size: 1.1rem;
                                        font-weight: 600;
                                        color: #333;
                                        margin: 0 0 6px 0;
                                    ">${exp.jobTitle || 'المسمى الوظيفي'}</h4>
                                    <div style="
                                        font-weight: 600;
                                        color: #1e40af;
                                        margin-bottom: 4px;
                                    ">${exp.company || 'اسم الشركة'}</div>
                                    <div style="
                                        font-size: 0.85rem;
                                        color: #666;
                                        margin-bottom: 8px;
                                        font-style: italic;
                                    ">
                                        ${exp.startDate || 'تاريخ البداية'} -
                                        ${exp.isCurrent ? 'حتى الآن' : (exp.endDate || 'تاريخ النهاية')}
                                    </div>
                                    ${exp.description ? `
                                        <div style="
                                            font-size: 0.9rem;
                                            line-height: 1.6;
                                            color: #555;
                                            text-align: justify;
                                        ">${exp.description}</div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${data.education && data.education.length > 0 ? `
                        <div class="cv-section" style="margin-bottom: 28px;">
                            <h3 style="
                                font-size: 1.3rem;
                                color: #1e40af;
                                margin-bottom: 12px;
                                padding-bottom: 6px;
                                border-bottom: 2px solid #e5e7eb;
                                font-weight: 600;
                            ">التعليم</h3>
                            ${data.education.map(edu => `
                                <div style="
                                    margin-bottom: 16px;
                                    padding: 14px;
                                    background: #f8fafc;
                                    border-radius: 8px;
                                    border-right: 4px solid #1e40af;
                                ">
                                    <h4 style="
                                        font-size: 1rem;
                                        font-weight: 600;
                                        color: #333;
                                        margin: 0 0 6px 0;
                                    ">${edu.degree || 'الدرجة العلمية'}</h4>
                                    <div style="
                                        font-weight: 600;
                                        color: #1e40af;
                                        margin-bottom: 4px;
                                    ">${edu.institution || 'اسم الجامعة'}</div>
                                    <div style="
                                        font-size: 0.85rem;
                                        color: #666;
                                        margin-bottom: 6px;
                                    ">${edu.year || 'سنة التخرج'}</div>
                                    ${edu.gpa ? `
                                        <div style="
                                            font-size: 0.85rem;
                                            color: #666;
                                            margin-bottom: 6px;
                                        ">المعدل: ${edu.gpa}</div>
                                    ` : ''}
                                    ${edu.details ? `
                                        <div style="
                                            font-size: 0.9rem;
                                            line-height: 1.5;
                                            color: #555;
                                        ">${edu.details}</div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${(data.skills && (data.skills.technical?.length > 0 || data.skills.soft?.length > 0)) ? `
                        <div class="cv-section" style="margin-bottom: 28px;">
                            <h3 style="
                                font-size: 1.3rem;
                                color: #1e40af;
                                margin-bottom: 12px;
                                padding-bottom: 6px;
                                border-bottom: 2px solid #e5e7eb;
                                font-weight: 600;
                            ">المهارات</h3>
                            <div style="
                                display: flex;
                                flex-wrap: wrap;
                                gap: 8px;
                            ">
                                ${data.skills.technical ? data.skills.technical.map(skill => `
                                    <span style="
                                        background: #1e40af;
                                        color: white;
                                        padding: 6px 12px;
                                        border-radius: 16px;
                                        font-size: 0.85rem;
                                        font-weight: 500;
                                    ">${skill}</span>
                                `).join('') : ''}
                                ${data.skills.soft ? data.skills.soft.map(skill => `
                                    <span style="
                                        background: #059669;
                                        color: white;
                                        padding: 6px 12px;
                                        border-radius: 16px;
                                        font-size: 0.85rem;
                                        font-weight: 500;
                                    ">${skill}</span>
                                `).join('') : ''}
                            </div>
                        </div>
                    ` : ''}

                    ${data.languages && data.languages.length > 0 ? `
                        <div class="cv-section">
                            <h3 style="
                                font-size: 1.3rem;
                                color: #1e40af;
                                margin-bottom: 12px;
                                padding-bottom: 6px;
                                border-bottom: 2px solid #e5e7eb;
                                font-weight: 600;
                            ">اللغات</h3>
                            <div style="
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                                gap: 12px;
                            ">
                                ${data.languages.map(lang => `
                                    <div style="
                                        display: flex;
                                        justify-content: space-between;
                                        align-items: center;
                                        padding: 10px 14px;
                                        background: #f8fafc;
                                        border-radius: 8px;
                                        border: 1px solid #e5e7eb;
                                    ">
                                        <span style="font-weight: 500; color: #333;">${lang.name || 'اسم اللغة'}</span>
                                        <span style="
                                            background: #1e40af;
                                            color: white;
                                            padding: 4px 8px;
                                            border-radius: 12px;
                                            font-size: 0.8rem;
                                            font-weight: 500;
                                        ">${lang.level || 'المستوى'}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(tempContainer);

        // Update progress
        window.app.updateProgressIndicator(20, 'جاري معالجة المحتوى...');

        // Wait for fonts and images to load
        setTimeout(() => {
            window.app.updateProgressIndicator(40, 'جاري تحويل المحتوى إلى صورة...');

            html2canvas(tempContainer.firstElementChild, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                width: 794,
                height: tempContainer.firstElementChild.scrollHeight
            }).then(canvas => {
                window.app.updateProgressIndicator(70, 'جاري إنشاء ملف PDF...');

                try {
                    const imgData = canvas.toDataURL('image/png', 1.0);
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    const pdfWidth = pdf.internal.pageSize.getWidth();
                    const pdfHeight = pdf.internal.pageSize.getHeight();
                    const imgWidth = canvas.width;
                    const imgHeight = canvas.height;

                    // Calculate dimensions to fit page
                    const ratio = Math.min(pdfWidth / (imgWidth * 0.264583), pdfHeight / (imgHeight * 0.264583));
                    const finalWidth = (imgWidth * 0.264583) * ratio;
                    const finalHeight = (imgHeight * 0.264583) * ratio;

                    // Center the image
                    const x = (pdfWidth - finalWidth) / 2;
                    const y = 10; // Small margin from top

                    pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

                    window.app.updateProgressIndicator(90, 'جاري حفظ الملف...');

                    const fileName = `${data.personal.fullName || 'السيرة_الذاتية'}.pdf`;
                    pdf.save(fileName);

                    window.app.updateProgressIndicator(100, 'تم التصدير بنجاح!');

                    setTimeout(() => {
                        window.app.hideProgressIndicator();
                        showNotification('تم تصدير ملف PDF بنجاح', 'success');
                    }, 1000);

                } catch (error) {
                    console.error('Error creating PDF:', error);
                    window.app.hideProgressIndicator();
                    showNotification('حدث خطأ أثناء إنشاء ملف PDF: ' + error.message, 'error');
                } finally {
                    // Clean up
                    document.body.removeChild(tempContainer);
                }

            }).catch(error => {
                console.error('Error generating canvas:', error);
                window.app.hideProgressIndicator();
                showNotification('حدث خطأ أثناء معالجة محتوى السيرة الذاتية: ' + error.message, 'error');
                document.body.removeChild(tempContainer);
            });
        }, 1000);

    } else {
        // Fallback without progress indicator
        showExportLoading('جاري إنشاء ملف PDF...');
        showNotification('وظيفة التصدير محدودة. يرجى تحديث الصفحة والمحاولة مرة أخرى.', 'warning');
        hideExportLoading();
    }
}

function exportToImage(format) {
    // Check if libraries are loaded
    if (typeof html2canvas === 'undefined') {
        showNotification('مكتبة تصدير الصور لم يتم تحميلها بعد. يرجى المحاولة مرة أخرى.', 'warning');
        return;
    }

    // Check if CV has content
    if (!window.cvBuilder || !window.cvBuilder.cvData || !window.cvBuilder.cvData.personal.fullName) {
        showNotification('لا توجد بيانات كافية للتصدير. يرجى إدخال معلوماتك الشخصية على الأقل.', 'warning');
        return;
    }

    // Close export modal
    closeExportModal();

    // Show progress indicator
    if (window.app) {
        const progressIndicator = window.app.showProgressIndicator(`جاري تحضير صورة ${format.toUpperCase()}...`, 0);

        // Create temporary CV element for export
        const tempContainer = document.createElement('div');
        tempContainer.style.cssText = `
            position: absolute;
            top: -9999px;
            left: -9999px;
            width: 794px;
            background: white;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        `;

        // Generate CV content (same as PDF but optimized for images)
        const data = window.cvBuilder.cvData;
        tempContainer.innerHTML = `
            <div class="cv-page image-export" style="
                padding: 50px;
                background: white;
                color: #333;
                line-height: 1.6;
                font-size: 16px;
                min-height: 1000px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            ">
                <div class="cv-header" style="
                    display: flex;
                    align-items: center;
                    gap: 30px;
                    margin-bottom: 40px;
                    padding-bottom: 25px;
                    border-bottom: 4px solid #1e40af;
                ">
                    ${data.personal.photo ? `
                        <div class="cv-photo" style="
                            width: 140px;
                            height: 140px;
                            border-radius: 50%;
                            overflow: hidden;
                            border: 4px solid #1e40af;
                            flex-shrink: 0;
                            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
                        ">
                            <img src="${data.personal.photo}" alt="Profile Photo" style="
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            ">
                        </div>
                    ` : ''}
                    <div class="cv-basic-info" style="flex: 1;">
                        <h1 style="
                            font-size: 2.5rem;
                            font-weight: 700;
                            color: #1e40af;
                            margin: 0 0 10px 0;
                            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
                        ">${data.personal.fullName}</h1>
                        <h2 style="
                            font-size: 1.6rem;
                            color: #666;
                            margin: 0 0 20px 0;
                            font-weight: 500;
                        ">${data.personal.jobTitle || ''}</h2>
                        <div class="cv-contact" style="
                            display: flex;
                            flex-wrap: wrap;
                            gap: 20px;
                            font-size: 1rem;
                            color: #555;
                        ">
                            ${data.personal.email ? `
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    background: #f8fafc;
                                    padding: 8px 12px;
                                    border-radius: 20px;
                                    border: 1px solid #e5e7eb;
                                ">
                                    <span style="color: #1e40af; font-size: 1.1rem;">📧</span>
                                    <span>${data.personal.email}</span>
                                </div>
                            ` : ''}
                            ${data.personal.phone ? `
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    background: #f8fafc;
                                    padding: 8px 12px;
                                    border-radius: 20px;
                                    border: 1px solid #e5e7eb;
                                ">
                                    <span style="color: #1e40af; font-size: 1.1rem;">📱</span>
                                    <span>${data.personal.phone}</span>
                                </div>
                            ` : ''}
                            ${data.personal.city ? `
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    background: #f8fafc;
                                    padding: 8px 12px;
                                    border-radius: 20px;
                                    border: 1px solid #e5e7eb;
                                ">
                                    <span style="color: #1e40af; font-size: 1.1rem;">📍</span>
                                    <span>${data.personal.city}</span>
                                </div>
                            ` : ''}
                            ${data.personal.website ? `
                                <div style="
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    background: #f8fafc;
                                    padding: 8px 12px;
                                    border-radius: 20px;
                                    border: 1px solid #e5e7eb;
                                ">
                                    <span style="color: #1e40af; font-size: 1.1rem;">🌐</span>
                                    <span>${data.personal.website}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                </div>

                <div class="cv-content">
                    ${data.summary ? `
                        <div class="cv-section" style="margin-bottom: 35px;">
                            <h3 style="
                                font-size: 1.5rem;
                                color: #1e40af;
                                margin-bottom: 15px;
                                padding-bottom: 8px;
                                border-bottom: 3px solid #e5e7eb;
                                font-weight: 600;
                                position: relative;
                            ">
                                الملخص المهني
                                <span style="
                                    position: absolute;
                                    bottom: -3px;
                                    right: 0;
                                    width: 60px;
                                    height: 3px;
                                    background: #1e40af;
                                "></span>
                            </h3>
                            <p style="
                                line-height: 1.8;
                                color: #555;
                                margin: 0;
                                text-align: justify;
                                font-size: 1.05rem;
                                background: #f8fafc;
                                padding: 20px;
                                border-radius: 10px;
                                border-right: 4px solid #1e40af;
                            ">${data.summary}</p>
                        </div>
                    ` : ''}

                    ${data.experience && data.experience.length > 0 ? `
                        <div class="cv-section" style="margin-bottom: 35px;">
                            <h3 style="
                                font-size: 1.5rem;
                                color: #1e40af;
                                margin-bottom: 15px;
                                padding-bottom: 8px;
                                border-bottom: 3px solid #e5e7eb;
                                font-weight: 600;
                                position: relative;
                            ">
                                الخبرات العملية
                                <span style="
                                    position: absolute;
                                    bottom: -3px;
                                    right: 0;
                                    width: 60px;
                                    height: 3px;
                                    background: #1e40af;
                                "></span>
                            </h3>
                            ${data.experience.map(exp => `
                                <div style="
                                    margin-bottom: 25px;
                                    padding: 20px;
                                    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
                                    border-radius: 12px;
                                    border-right: 5px solid #1e40af;
                                    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                                ">
                                    <h4 style="
                                        font-size: 1.25rem;
                                        font-weight: 600;
                                        color: #333;
                                        margin: 0 0 8px 0;
                                    ">${exp.jobTitle || 'المسمى الوظيفي'}</h4>
                                    <div style="
                                        font-weight: 600;
                                        color: #1e40af;
                                        margin-bottom: 6px;
                                        font-size: 1.05rem;
                                    ">${exp.company || 'اسم الشركة'}</div>
                                    <div style="
                                        font-size: 0.95rem;
                                        color: #666;
                                        margin-bottom: 10px;
                                        font-style: italic;
                                        background: #e5e7eb;
                                        padding: 6px 12px;
                                        border-radius: 15px;
                                        display: inline-block;
                                    ">
                                        ${exp.startDate || 'تاريخ البداية'} -
                                        ${exp.isCurrent ? 'حتى الآن' : (exp.endDate || 'تاريخ النهاية')}
                                    </div>
                                    ${exp.description ? `
                                        <div style="
                                            font-size: 1rem;
                                            line-height: 1.7;
                                            color: #555;
                                            text-align: justify;
                                            margin-top: 10px;
                                        ">${exp.description}</div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${data.education && data.education.length > 0 ? `
                        <div class="cv-section" style="margin-bottom: 35px;">
                            <h3 style="
                                font-size: 1.5rem;
                                color: #1e40af;
                                margin-bottom: 15px;
                                padding-bottom: 8px;
                                border-bottom: 3px solid #e5e7eb;
                                font-weight: 600;
                                position: relative;
                            ">
                                التعليم
                                <span style="
                                    position: absolute;
                                    bottom: -3px;
                                    right: 0;
                                    width: 60px;
                                    height: 3px;
                                    background: #1e40af;
                                "></span>
                            </h3>
                            ${data.education.map(edu => `
                                <div style="
                                    margin-bottom: 20px;
                                    padding: 18px;
                                    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
                                    border-radius: 12px;
                                    border-right: 5px solid #1e40af;
                                    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                                ">
                                    <h4 style="
                                        font-size: 1.15rem;
                                        font-weight: 600;
                                        color: #333;
                                        margin: 0 0 8px 0;
                                    ">${edu.degree || 'الدرجة العلمية'}</h4>
                                    <div style="
                                        font-weight: 600;
                                        color: #1e40af;
                                        margin-bottom: 6px;
                                        font-size: 1rem;
                                    ">${edu.institution || 'اسم الجامعة'}</div>
                                    <div style="
                                        font-size: 0.9rem;
                                        color: #666;
                                        margin-bottom: 8px;
                                        background: #e5e7eb;
                                        padding: 4px 10px;
                                        border-radius: 12px;
                                        display: inline-block;
                                    ">${edu.year || 'سنة التخرج'}</div>
                                    ${edu.gpa ? `
                                        <div style="
                                            font-size: 0.9rem;
                                            color: #666;
                                            margin-bottom: 8px;
                                            font-weight: 500;
                                        ">المعدل: ${edu.gpa}</div>
                                    ` : ''}
                                    ${edu.details ? `
                                        <div style="
                                            font-size: 0.95rem;
                                            line-height: 1.6;
                                            color: #555;
                                            margin-top: 8px;
                                        ">${edu.details}</div>
                                    ` : ''}
                                </div>
                            `).join('')}
                        </div>
                    ` : ''}

                    ${(data.skills && (data.skills.technical?.length > 0 || data.skills.soft?.length > 0)) ? `
                        <div class="cv-section" style="margin-bottom: 35px;">
                            <h3 style="
                                font-size: 1.5rem;
                                color: #1e40af;
                                margin-bottom: 15px;
                                padding-bottom: 8px;
                                border-bottom: 3px solid #e5e7eb;
                                font-weight: 600;
                                position: relative;
                            ">
                                المهارات
                                <span style="
                                    position: absolute;
                                    bottom: -3px;
                                    right: 0;
                                    width: 60px;
                                    height: 3px;
                                    background: #1e40af;
                                "></span>
                            </h3>
                            <div style="
                                display: flex;
                                flex-wrap: wrap;
                                gap: 12px;
                            ">
                                ${data.skills.technical ? data.skills.technical.map(skill => `
                                    <span style="
                                        background: linear-gradient(135deg, #1e40af, #3b82f6);
                                        color: white;
                                        padding: 10px 16px;
                                        border-radius: 20px;
                                        font-size: 0.95rem;
                                        font-weight: 500;
                                        box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
                                    ">${skill}</span>
                                `).join('') : ''}
                                ${data.skills.soft ? data.skills.soft.map(skill => `
                                    <span style="
                                        background: linear-gradient(135deg, #059669, #10b981);
                                        color: white;
                                        padding: 10px 16px;
                                        border-radius: 20px;
                                        font-size: 0.95rem;
                                        font-weight: 500;
                                        box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
                                    ">${skill}</span>
                                `).join('') : ''}
                            </div>
                        </div>
                    ` : ''}

                    ${data.languages && data.languages.length > 0 ? `
                        <div class="cv-section">
                            <h3 style="
                                font-size: 1.5rem;
                                color: #1e40af;
                                margin-bottom: 15px;
                                padding-bottom: 8px;
                                border-bottom: 3px solid #e5e7eb;
                                font-weight: 600;
                                position: relative;
                            ">
                                اللغات
                                <span style="
                                    position: absolute;
                                    bottom: -3px;
                                    right: 0;
                                    width: 60px;
                                    height: 3px;
                                    background: #1e40af;
                                "></span>
                            </h3>
                            <div style="
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
                                gap: 15px;
                            ">
                                ${data.languages.map(lang => `
                                    <div style="
                                        display: flex;
                                        justify-content: space-between;
                                        align-items: center;
                                        padding: 12px 16px;
                                        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
                                        border-radius: 12px;
                                        border: 2px solid #e5e7eb;
                                        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
                                    ">
                                        <span style="font-weight: 600; color: #333; font-size: 1rem;">${lang.name || 'اسم اللغة'}</span>
                                        <span style="
                                            background: linear-gradient(135deg, #1e40af, #3b82f6);
                                            color: white;
                                            padding: 6px 12px;
                                            border-radius: 15px;
                                            font-size: 0.85rem;
                                            font-weight: 500;
                                            box-shadow: 0 2px 6px rgba(30, 64, 175, 0.3);
                                        ">${lang.level || 'المستوى'}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        document.body.appendChild(tempContainer);

        // Update progress
        window.app.updateProgressIndicator(25, 'جاري معالجة المحتوى...');

        // Wait for fonts and images to load
        setTimeout(() => {
            window.app.updateProgressIndicator(50, `جاري تحويل المحتوى إلى صورة ${format.toUpperCase()}...`);

            html2canvas(tempContainer.firstElementChild, {
                scale: 3, // Higher scale for better quality
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                width: 794,
                height: tempContainer.firstElementChild.scrollHeight
            }).then(canvas => {
                window.app.updateProgressIndicator(80, `جاري حفظ صورة ${format.toUpperCase()}...`);

                try {
                    const quality = format === 'jpg' ? 0.95 : 1.0;
                    const mimeType = format === 'jpg' ? 'image/jpeg' : 'image/png';

                    canvas.toBlob((blob) => {
                        const link = document.createElement('a');
                        const fileName = `${data.personal.fullName || 'السيرة_الذاتية'}.${format}`;

                        link.download = fileName;
                        link.href = URL.createObjectURL(blob);

                        // Trigger download
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // Clean up
                        URL.revokeObjectURL(link.href);

                        window.app.updateProgressIndicator(100, 'تم التصدير بنجاح!');

                        setTimeout(() => {
                            window.app.hideProgressIndicator();
                            showNotification(`تم تصدير صورة ${format.toUpperCase()} بنجاح`, 'success');
                        }, 1000);

                    }, mimeType, quality);

                } catch (error) {
                    console.error('Error creating image:', error);
                    window.app.hideProgressIndicator();
                    showNotification('حدث خطأ أثناء إنشاء الصورة: ' + error.message, 'error');
                } finally {
                    // Clean up
                    document.body.removeChild(tempContainer);
                }

            }).catch(error => {
                console.error('Error generating canvas:', error);
                window.app.hideProgressIndicator();
                showNotification('حدث خطأ أثناء معالجة محتوى السيرة الذاتية: ' + error.message, 'error');
                document.body.removeChild(tempContainer);
            });
        }, 1000);

    } else {
        // Fallback without progress indicator
        showExportLoading(`جاري إنشاء صورة ${format.toUpperCase()}...`);
        showNotification('وظيفة التصدير محدودة. يرجى تحديث الصفحة والمحاولة مرة أخرى.', 'warning');
        hideExportLoading();
    }
}

function generateQRCode() {
    // Close export modal
    document.querySelector('.modal')?.remove();

    if (!window.cvBuilder.cvData.personal.email && !window.cvBuilder.cvData.personal.phone) {
        showNotification('يرجى إضافة معلومات الاتصال أولاً', 'warning');
        return;
    }

    if (window.generateQRCode) {
        window.generateQRCode();
    } else {
        showNotification('وظيفة إنشاء رمز QR غير متاحة حالياً', 'error');
    }
}

function showExportLoading(message) {
    const loading = document.createElement('div');
    loading.id = 'exportLoading';
    loading.className = 'export-loading';
    loading.innerHTML = `
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>${message}</p>
        </div>
    `;
    document.body.appendChild(loading);
}

function hideExportLoading() {
    const loading = document.getElementById('exportLoading');
    if (loading) {
        loading.remove();
    }
}

function showNotification(message, type = 'info') {
    if (window.app && window.app.showNotification) {
        window.app.showNotification(message, type);
    } else {
        // Fallback notification
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--bg-card);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            z-index: 9999;
            max-width: 300px;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
}

// Initialize CV Builder when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvBuilder = new CVBuilder();

    // Setup skills input after DOM is loaded
    setTimeout(() => {
        setupSkillsInput();
    }, 1000);

    // Initialize CV Preview for export functionality
    setTimeout(() => {
        if (typeof CVPreview !== 'undefined') {
            window.cvPreview = new CVPreview();
        }
    }, 2000);
});
